<?php
namespace App\Filament\Admin\Widgets;

use App\Enums\TutorActivities;
use App\Models\AskMe\Question;
use App\Models\AskMe\Tutor;
use App\Models\AskMe\TutorActivity;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

/**
 * Admin General Stats Widget
 *
 * Displays general statistics and metrics on the admin dashboard.
 */
class AdminGeneralStats extends BaseWidget
{

    /**
     * Get the general statistics to display in the widget
     *
     * - Question statistics (total, answered, unanswered)
     * - Tutor statistics
     * - Activity statistics
     * - Average answer time
     *
     * @return array The stats configuration
     */
    protected function getStats(): array
    {
        // Calculate date for "this week" filter
        $weekAgo = now()->subDays(7);

        // Initialize query builders for reuse
        $questions = Question::query();
        $activities = TutorActivity::query();

        // Return array of stat cards to display in the widget
        return [
            // Total questions stat card with 7-day trend chart
            Stat::make(__('filament-panels.dashboard.total_questions'), $questions->count())
                ->description(__('filament-panels.dashboard.total_questions_desc'))
                ->icon('heroicon-o-question-mark-circle')
                ->color('primary')
                ->chart($this->getTrend(
                    // Daily count of all questions
                    fn($date) =>
                    Question::whereDate('created_at', $date)->count()
                )),

            // Answered questions stat card with 7-day trend chart
            Stat::make(__('filament-panels.dashboard.answered_questions'), $questions->whereNotNull('answer_text')->count())
                ->description(__('filament-panels.dashboard.answered_questions_desc'))
                ->icon('heroicon-o-check-circle')
                ->color('success')
                ->chart($this->getTrend(
                    // Daily count of answered questions
                    fn($date) =>
                    Question::whereDate('created_at', $date)->whereNotNull('answer_text')->count()
                )),

            // Unanswered questions stat card with 7-day trend chart
            Stat::make(__('filament-panels.dashboard.unanswered_questions'), $questions->whereNull('answer_text')->count())
                ->description(__('filament-panels.dashboard.unanswered_questions_desc'))
                ->icon('heroicon-o-exclamation-circle')
                ->color('warning')
                ->chart($this->getTrend(
                    // Daily count of unanswered questions
                    fn($date) =>
                    Question::whereDate('created_at', $date)->whereNull('answer_text')->count()
                )),

            // Questions received in the past week stat card
            Stat::make(__('filament-panels.dashboard.questions_this_week'), Question::where('created_at', '>=', $weekAgo)->count())
                ->description(__('filament-panels.dashboard.questions_this_week_desc'))
                ->icon('heroicon-o-calendar')
                ->color('info'),

            // Average answer time stat card
            Stat::make(__('filament-panels.dashboard.avg_answer_time'), $this->getAverageAnswerTime())
                ->description(__('filament-panels.dashboard.avg_answer_time_desc'))
                ->icon('heroicon-o-clock')
                ->color('gray'),

            // Total tutors stat card
            Stat::make(__('filament-panels.dashboard.total_tutors'), Tutor::count())
                ->description(__('filament-panels.dashboard.total_tutors_desc'))
                ->icon('heroicon-o-user-group')
                ->color('primary'),

            // Question acceptance activities stat card with 7-day trend chart
            Stat::make(__('filament-panels.dashboard.accepted_activities'), $activities->where('action', TutorActivities::Accept)->count())
                ->description(__('filament-panels.dashboard.accepted_activities_desc'))
                ->icon('heroicon-o-check')
                ->color('info')
                ->chart($this->getTrend(
                    // Daily count of question acceptance activities
                    fn($date) =>
                    TutorActivity::where('action', TutorActivities::Accept)->whereDate('created_at', $date)->count()
                )),

            // Question answering activities stat card with 7-day trend chart
            Stat::make(__('filament-panels.dashboard.answered_activities'), $activities->where('action', TutorActivities::Answer)->count())
                ->description(__('filament-panels.dashboard.answered_activities_desc'))
                ->icon('heroicon-o-pencil-square')
                ->color('success')
                ->chart($this->getTrend(
                    // Daily count of question answering activities
                    fn($date) =>
                    TutorActivity::where('action', TutorActivities::Answer)->whereDate('created_at', $date)->count()
                )),

            // Answer editing activities stat card with 7-day trend chart
            Stat::make(__('filament-panels.dashboard.edited_activities'), $activities->where('action', TutorActivities::Edit)->count())
                ->description(__('filament-panels.dashboard.edited_activities_desc'))
                ->icon('heroicon-o-pencil')
                ->color('warning')
                ->chart($this->getTrend(
                    // Daily count of answer editing activities
                    fn($date) =>
                    TutorActivity::where('action', TutorActivities::Edit)->whereDate('created_at', $date)->count()
                )),
        ];
    }

    /**
     * Calculate the average time it takes for tutors to answer questions
     *
     * This method calculates the average time difference between when a question
     * is created and when it receives its first answer from a tutor.
     *
     * @return string Formatted average answer time in minutes or placeholder if no data
     */
    protected function getAverageAnswerTime(): string
    {
        // Calculate average time difference between question creation and answer
        $avgSeconds = TutorActivity::query()
            ->where('tutor_activities.action', TutorActivities::Answer) // Only answer activities
            ->whereNotNull('tutor_activities.created_at') 
            ->join('questions', 'tutor_activities.question_id', '=', 'questions.id') // Join with questions
            // Calculate time difference in seconds between question creation and answer
            ->avg(DB::raw('TIMESTAMPDIFF(SECOND, questions.created_at, tutor_activities.created_at)'));

        // Format the result in minutes or return placeholder if no data
        return $avgSeconds ? round($avgSeconds / 60, 1) . ' min' : '—';
    }

    /**
     * Calculate trend data for the past 7 days
     *
     * This helper method generates an array of values for the past 7 days
     * by applying the provided callback function to each day.
     *
     * @param callable $queryCallback Function to execute for each day
     * @return array Array of trend values for the past 7 days
     */
    protected function getTrend(callable $queryCallback): array
    {
        $trend = [];

        // Loop through the past 7 days (6 days ago to today)
        for ($i = 6; $i >= 0; $i--) {
            // Get the date for this iteration
            $date = now()->subDays($i)->startOfDay();

            // Execute the callback for this date and add the result to the trend array
            $trend[] = $queryCallback($date);
        }

        return $trend;
    }
}
