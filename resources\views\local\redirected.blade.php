<x-layout dir="rtl" title="تم توجيهك لخدمة T-lync لتسديد قيمة الأشتراك" subtitle="لأستلام معلومات الدخول (أسم المستخدم و كلمة المرور) تأكد من الضغط علي زر العودة كما موضح في الصورة اسفله">
    <section class="container text-center py-5">
        <div class="card">
            <div class="card-body d-none" id="redirectUser">
                <h1 class="card-title">اذا لم يتم توجيهك الي خدمة T-lync لتسديد قيمةالإشترك اضعط على إعادة التوجيه</h1>
                <a href="{{ $url }}" class="btn btn-lg btn-primary">إعادة التوجيه</a>
            </div>
    </section>
    <section class="container text-center py-5">
        <img src="{{ asset('assets/images/redirected.png') }}" class="img-fluid" alt="تم توجيهك لخدمة T-lync لتسديد قيمة الأشتراك">
    </section>
    <script>
        // Attempt to open the new tab
        var newWindow = window.open('{{ $url }}', '_blank');

        // Check if the popup was blocked
        if (!newWindow || newWindow.closed || typeof newWindow.closed == 'undefined') {
            // Remove the d-none class to show the redirectUser element
            document.getElementById('redirectUser').classList.remove('d-none');
        }
    </script>
</x-layout>
