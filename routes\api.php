<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\InitiatePaymentController;
use App\Http\Controllers\StripePaymentController;
use App\Http\Controllers\WhatsAppController;
use Twilio\Rest\Content\V1\WhatsappCard;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');


Route::post('/tlync/transaction', [InitiatePaymentController::class, 'transaction'])->name('transaction');

Route::post('/webhook',StripePaymentController::class.'@webhook');

//WhatsApp api
Route::post('/whatsapp',WhatsAppController::class.'@handleWebhook');
Route::get('/whatsapp',WhatsAppController::class.'@verifyWebhook');

