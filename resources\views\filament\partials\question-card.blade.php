<div class="bg-white shadow-sm border border-gray-200 rounded-xl p-4 flex gap-4 items-start">
    {{-- Question Image --}}
    @if ($question->image_url)
        <img src="{{ asset($question->image_url) }}" class="w-32 h-32 object-cover rounded" alt="Question Image">
    @else
        <div class="w-32 h-32 flex items-center justify-center bg-gray-100 text-sm text-gray-400 rounded">
            No Image
        </div>
    @endif

    <div class="flex-1">
        {{-- Question Text --}}
        <div class="text-lg font-bold text-primary">{{ $question->question_text }}</div>

        {{-- Student Info --}}
        <div class="text-sm text-gray-500 mt-1">
            {{ __('filament-panels.question.fields.name') }}:
            <strong>{{ $question->student->name ?? 'Unknown' }}</strong>
        </div>
        {{-- grade Info --}}
        <div class="text-sm text-gray-500 mt-1">
            <strong>{{ $question->grade->name ?? 'Unknown' }}</strong> -
            <strong>{{ $question->subject->name ?? 'Unknown' }}</strong>
        </div>

        {{-- Timestamp --}}
        <div class="text-xs text-gray-400 mt-1">
            {{ $question->created_at->format('Y-m-d H:i') }}
        </div>

        {{-- Status + Button --}}
        @php
            $answered = $question->answer_text || $question->answer_url;
        @endphp

        <div class="mt-2">
            {{-- Status Badge --}}
            <div class="inline-block mb-2">
                <x-filament::badge
                    :color="$answered ? 'success' : 'danger'"
                    class="px-3 py-1 text-xs font-semibold rounded-full">
                    {{ $answered ? __('filament-panels.question.fields.answered') : __('filament-panels.question.fields.not_answered') }}
                </x-filament::badge>
            </div>

            {{-- Answer Button --}}
            <div>
                <x-filament::button
                    href="{{ url('/tutor/answer-question/' . $question->id) }}"
                    :color="$answered ? 'success' : 'warning'"
                    size="sm"
                    tag="a">
                    {{ $answered ?__('filament-panels.question.action.edit_answer') : __('filament-panels.question.action.add_answer') }}
                </x-filament::button>
            </div>
        </div>
    </div>
</div>
