<?php

namespace App\Filament\Admin\Resources;

use App\Enums\SubscriptionPlanType;
use App\Filament\Admin\Resources\SubscriptionPackageResource\Pages;
use App\Models\AskMe\SubscriptionPackage;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Table;

/**
 * Subscription Package Resource
 *
 * Manages subscription packages in the admin panel.
 * Provides functionality to create, view, edit, and delete subscription packages
 */
class SubscriptionPackageResource extends Resource
{
    /**
     * The model that this resource manages
     */
    protected static ?string $model = SubscriptionPackage::class;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    /**
     * Get the title for this resource page
     *
     * @return string The localized title
     */
    public function getTitle(): string
    {
        return __('filament-panels.subscription_pachage.plural');
    }

    /**
     * Get the singular label for this resource
     *
     * @return string The localized singular label
     */
    public static function getModelLabel(): string
    {
        return __('filament-panels.subscription_pachage.singular');
    }

    /**
     * Get the plural label for this resource
     *
     * @return string The localized plural label
     */
    public static function getPluralModelLabel(): string
    {
        return __('filament-panels.subscription_pachage.plural');
    }

    /**
     * Get the navigation label for this resource
     *
     * @return string The localized navigation label
     */
    public static function getNavigationLabel(): string
    {
        return __('filament-panels.subscription_pachage.title');
    }

    /**
     * Define the form structure for creating and editing subscription packages
     *
     * @param Form $form The Filament form instance
     * @return Form The configured form
     */
    public static function form(Form $form): Form
    {
        return $form->schema([
            // Package name field
            TextInput::make('name')
                ->required()
                ->minLength(3)
                ->maxLength(100)
                ->label(__('filament-panels.subscription_pachage.fields.name')),

            // Question limit field - how many questions are included in this package
            TextInput::make('question_limit')
                ->numeric()
                ->minValue(1)
                ->required()
                ->label(__('filament-panels.subscription_pachage.fields.question_limit')),

            // Price field
            TextInput::make('price')
                ->numeric()
                ->minValue(0)
                ->label(__('filament-panels.subscription_pachage.fields.price'))
                ->required(),

            // Package type selection (LOCAL or INTERNATIONAL)
            Select::make('type')
                ->required()
                ->options(collect(SubscriptionPlanType::cases())->mapWithKeys(fn($case) => [
                    $case->value => $case->label()
                ])->toArray())
                ->label(__('filament-panels.subscription_pachage.fields.type')),

            // Active status toggle
            Toggle::make('is_active')
                ->label(__('filament-panels.subscription_pachage.fields.status'))
                ->default(1),
        ]);
    }

    /**
     * Define the table structure for displaying subscription packages
     *
     * @param Table $table The Filament table instance
     * @return Table The configured table
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                // Package name column
                TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->label(__('filament-panels.subscription_pachage.fields.name')),

                // Question limit column
                TextColumn::make('question_limit')
                    ->sortable()
                    ->label(__('filament-panels.subscription_pachage.fields.question_limit')),

                // Price column with currency formatting based on package type
                TextColumn::make('price')
                    ->formatStateUsing(function ($state, $record) {
                        // Determine currency symbol based on package type
                        $currency = match ($record->type) {
                            SubscriptionPlanType::LOCAL->value => 'د.ل', // Libyan Dinar
                            SubscriptionPlanType::INTERNATIONAL->value => '$', // US Dollar
                            default => '',
                        };

                        // Format price with currency symbol
                        return $currency . ' ' . number_format($state, 2);
                    })
                    ->sortable()
                    ->label(__('filament-panels.subscription_pachage.fields.price')),

                // Package type column with color-coded badges
                TextColumn::make('type')
                    ->label(__('filament-panels.subscription_pachage.fields.type'))
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'LOCAL' => 'primary',
                        'INTERNATIONAL' => 'success',
                        default => 'gray',
                    })
                    ->sortable(),

                // Active status toggle column
                ToggleColumn::make('is_active')
                    ->label(__('filament-panels.subscription_pachage.fields.status'))
            ])
            ->actions([
                // Row-level actions
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->bulkActions([
                // Actions that can be performed on multiple records
                DeleteBulkAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
        ];
    }

    /**
     * Define the pages for the Subscription Package resource
     *
     * @return array The pages configuration
     */
    public static function getPages(): array
    {
        return [
            // List page (main index view)
            'index' => Pages\ListSubscriptionPackages::route('/'),
            // Create new package page
            'create' => Pages\CreateSubscriptionPackage::route('/create'),
            // Edit existing package page
            'edit' => Pages\EditSubscriptionPackage::route('/{record}/edit'),
        ];
    }
}
