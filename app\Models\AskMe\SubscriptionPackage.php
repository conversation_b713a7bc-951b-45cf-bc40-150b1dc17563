<?php

namespace App\Models\AskMe;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SubscriptionPackage extends Model
{
    protected $fillable = ['name', 'question_limit', 'price', 'type', 'is_active'];
    protected $connection = 'mysql_secondary';

    protected $casts = [
        'id'=>'integer',
        'price' => 'decimal:2',
        'question_limit' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',

    ];

    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class, 'subscription_packages_id');
    }
    public function transactions(): <PERSON><PERSON>any
    {
        return $this->hasMany(Transaction::class, 'subscription_packages_id');
    }
}
