<?php

namespace App\Services;

use App\Enums\UserState;
use App\Models\AskMe\Student;
use App\Models\AskMe\Tutor;
use App\Services\SubscriptionService;
use App\Services\QuestionService;
use App\Services\TutorService;
use Illuminate\Support\Facades\Log;

/**
 * AskMeRouter Service
 *
 * This service is router for the WhatsApp messaging system.
 * It receives incoming messages from WhatsApp, identifies the sender type (student or tutor),
 * determines their current state, and routes the message to the appropriate service handler.
 *
 */
class AskMeRouter
{
    /**
     * Service for handling subscription-related messages and actions
     *
     * @var SubscriptionService
     */
    protected $subscriptionService;

    /**
     * Service for handling question-related messages and actions
     *
     * @var QuestionService
     */
    protected $questionService;

    /**
     * Service for handling tutor-specific messages and actions
     *
     * @var TutorService
     */
    protected $tutorService;



    /**
     * Constructor
     *
     *
     * @param SubscriptionService $subscriptionService Service for subscription-related functionality
     * @param QuestionService $questionService Service for question-related functionality
     * @param TutorService $tutorService Service for tutor-related functionality
     */
    public function __construct(
        SubscriptionService $subscriptionService,
        QuestionService $questionService,
        TutorService $tutorService
    ) {
        $this->subscriptionService = $subscriptionService;
        $this->questionService = $questionService;
        $this->tutorService = $tutorService;
    }

    /**
     * Process incoming WhatsApp message
     *
     * This is the main entry point for handling incoming WhatsApp messages.
     * It extracts relevant data from the payload, identifies the sender,
     * and routes the message to the appropriate service based on:
     * 1. Whether the sender is a student or tutor
     * 2. The current state of the sender
     * 3. Whether the message is interactive (button/list selection) or text
     *
     * @param array $value The WhatsApp message payload
     * @return mixed Response from the service handler or null
     */
    public function process(array $value)
    {
        // Check if the payload contains a message
        if (!isset($value['messages']) || empty($value['messages'])) {
            return;
        }

        // Extract the values from the payload
        $message = $value['messages'][0];
        $from = $message['from'];  // Sender's phone number
        $name = $value['contacts'][0]['profile']['name'] ?? 'عميل واتساب';  // Sender's name or default
        $interactive = $message['interactive'] ?? null;  // Interactive message data if present

        // Extract selection ID from interactive message (button or list selection)
        $selection = $interactive['list_reply']['id'] ?? $interactive['button_reply']['id'] ?? null;

        // Look up the sender in both student and tutor tables
        $student = Student::where('number', $from)->first();
        $tutor = Tutor::where('phone', $from)
            ->whereHas('user', function ($query) {
                $query->where('is_active', 1);  
            })
            ->first();

        // Handle text messages based on sender type and state
        if (!$interactive) {
            return match (true) {
                // Tutor message handling based on tutor state
                !empty($tutor) && $tutor->status === UserState::Init->value =>
                    $this->tutorService->defaultMessage($from),

                !empty($tutor) && $tutor->status === UserState::AwaitingAnswer->value =>
                    $this->tutorService->saveTutorAnswer($from, $message),

                // Student message handling based on student state
                // New user (not in system yet)
                empty($student) && empty($tutor) =>
                    $this->subscriptionService->sendWelcomeMessage($from, $name),

                // Existing student in initial state
                !empty($student) && $student->status === UserState::Init->value =>
                    $this->subscriptionService->sendWelcomeMessage($from, $name),

                // Student is expected to submit a question
                !empty($student) && $student->status === UserState::AwaitingQuestion->value =>
                    $this->questionService->handleQuestionSubmission($message),

                // Student is expected to submit a comment
                !empty($student) && $student->status === UserState::AwaitingComment->value =>
                    $this->questionService->saveComment($from, $message),

                default => null
            };
        }

        // Handle interactive messages (button/list selections)
        return match (true) {
            // Subscription-related actions
            $selection === 'subscription_info' =>
                $this->subscriptionService->sendSubscriptionInfo($from),

            $selection === 'subscription_history' =>
                $this->subscriptionService->sendSubscriptionHistory($from),

            $selection === 'subscribe_now' =>
                $this->subscriptionService->subscribeNow($from),

            // Question-related actions for students
            $selection === 'ask_question' =>
                $this->questionService->sendGradeMessage($from),

            $selection === 'view_questions' =>
                $this->questionService->sendQuestionList($from),

            $selection === 'view_grades_subjects' =>
                $this->questionService->viewAvailableGradeAndSubject($from),

            // Grade and subject selection handling
            str_starts_with($selection, 'grade_') =>
                $this->questionService->sendSubjectMessage($from, str_replace('grade_', '', $selection)),

            str_starts_with($selection, 'subject_') =>
                $this->questionService->sendQuestionMessage($from, $selection),

            str_starts_with($selection, 'accept_') =>
                $this->questionService->acceptQuestion($from, str_replace('accept_', '', $selection)),

            // Tutor-specific actions
            $selection === 'tutor_questions_list' =>
                $this->tutorService->sendQuestionList($from),

            str_starts_with($selection, 'tutor_view_question_') =>
                $this->tutorService->questionDetails($from, str_replace('tutor_view_question_', '', $selection)),

            str_starts_with($selection, 'answer_') =>
                $this->tutorService->answerQuestion($from, str_replace('answer_', '', $selection)),

            str_starts_with($selection, 'confirm_answer_') =>
                $this->tutorService->confirmAndSendAnswer($from, str_replace('confirm_answer_', '', $selection)),

            str_starts_with($selection, 'cancel_answer_') =>
                $this->tutorService->cancelAnswer($from),

            // Student comment actions
            str_starts_with($selection, 'add_comment_') =>
                $this->questionService->addComment($from, str_replace('add_comment_', '', $selection)),

            str_starts_with($selection, 'student_view_question_') =>
                $this->questionService->questionDetails($from, str_replace('student_view_question_', '', $selection)),

            default => null
        };
    }
}
