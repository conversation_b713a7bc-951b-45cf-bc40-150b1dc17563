<x-filament::page>
    <div class="space-y-6">
        @forelse ($questions as $question)
            <div class="bg-white p-6 rounded-xl border shadow-sm">
                {{-- Header: Question + Edit Button --}}
                <div class="flex justify-between items-start">
                    <div>
                        <h2 class="text-lg font-bold text-primary mb-2">{{ $question->question_text }}</h2>
                        <div class="text-sm text-gray-500 mb-4">
                            {{__('filament-panels.question.fields.name')}}: <strong>{{ $question->student->name ?? 'Unknown' }}</strong>
                        </div>
                    </div>

                    {{-- View/Edit Button --}}
                    <x-filament::button
                    href="{{ url('/tutor/answer-question/' . $question->id) }}"
                        color="primary"
                        size="sm"
                        tag="a">
                        {{__('filament-panels.question.action.view')}}
                    </x-filament::button>
                </div>

                {{-- Comments List --}}
                @forelse ($question->comments as $comment)
                    <div class="border-t py-2">
                        <p class="text-gray-700 text-sm">{{ $comment->comment }}</p>
                        <span class="text-xs text-gray-400">{{ $comment->created_at->format('Y-m-d H:i') }}</span>
                    </div>
                @empty
                    <p class="text-gray-400 italic">No comments for this question.</p>
                @endforelse
            </div>
        @empty
            <div class="text-gray-500 text-center">💬</div>
        @endforelse
    </div>
</x-filament::page>
