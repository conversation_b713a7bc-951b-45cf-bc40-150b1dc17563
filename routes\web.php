<?php

use App\Http\Controllers\Dashboard\ExportController;
use App\Http\Controllers\Dashboard\InvoiceController;
use App\Http\Controllers\Dashboard\LanguageController;
use App\Http\Controllers\Dashboard\MetaCatalogTemplateController;
use App\Http\Controllers\StripePaymentController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\InitiatePaymentController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\WhatsAppController;

Auth::routes();

// Default route for the homepage
Route::get('/', [App\Http\Controllers\HomeController::class, 'index'])->name('homePage');
Route::get('/plans', [App\Http\Controllers\HomeController::class, 'index'])->name('home');
Route::get('/plans/{type}', [HomeController::class, 'getSubscriptionPlans'])->name('getPlans');

Route::get('/verify-user/{phone}', [App\Http\Controllers\HomeController::class, 'createAndSendOtp'])->name('createAndSendOtp');
Route::get('/verify-otp/{phone}/{otp}', [App\Http\Controllers\HomeController::class, 'verifyOtp'])->name('verifyOtp');

// Instructions page route
Route::get('/instructions', [HomeController::class, 'showInstructions'])->name('showInstructions');

// T-lync routes
Route::post('/tlync', [InitiatePaymentController::class, 'initiate'])->name('initiatePayment');
Route::get('/getReceipt', [InitiatePaymentController::class, 'getReceipt'])->name('getReceipt');

// Routes for whatsapp pay
Route::post('/whatsapp/payment', [WhatsAppController::class, 'initiateTlyncPayment'])->name('initiateWhatsappPayment');
Route::get('/whatsapp/payment/success/{customRef}', [WhatsAppController::class, 'showPaymentSucceeded'])->name('showPaymentSuccessd');
Route::get('/whatsapp/payment/failed/{customRef}',  [WhatsAppController::class, 'showPaymentFailed'])->name('showPaymentFailed');

// Success payment route
Route::get('/payment/success', [InitiatePaymentController::class, 'showPaymentSucceeded'])->name('showPaymentSuccessd');

// Failed payment route
Route::get('/payment/failed',  [InitiatePaymentController::class, 'showPaymentFailed'])->name('showPaymentFailed');

//stripe
Route::post('/stripe',StripePaymentController::class.'@checkout')->name('stripe.checkout');
Route::get('/stripe/success/{CHECKOUT_SESSION_ID}',StripePaymentController::class.'@verifyPayment')->name('stripe.success');


//For Locale
Route::get('/switch-lang/{locale}', [LanguageController::class, 'switchLang'])->name('switch-lang');

Route::get('/admin/reports/generate/{type}/{range}/{month?}', [ExportController::class, 'generate'])
->name('admin.reports.dynamic.pdf');


