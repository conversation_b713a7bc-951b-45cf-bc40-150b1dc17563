<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::connection('mysql_secondary')->create('subscription_packages', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->integer('question_limit');
            $table->decimal('price', 8, 2);
            $table->enum('type', ['LOCAL', 'INTERNATIONAL']);
            $table->boolean('is_active')->default(1);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('subscription_packages');
    }
};
