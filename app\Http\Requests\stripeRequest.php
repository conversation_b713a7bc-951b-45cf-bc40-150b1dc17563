<?php

namespace App\Http\Requests;
use App\Rules\validateOtp;

use Illuminate\Foundation\Http\FormRequest;

class stripeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {

        return [
            'plan_id' => 'required|integer|exists:subscription_plans,id',
            'phone' => 'required|string|regex:/^\+?\d+$/',
            'email' => 'required|email',
        ];
    }

    public function messages(): array
    {
        return [
            'plan_id.required' => 'يجب تحديد باقة قبل الضغط على زر التالي',
            'phone.required' => 'حقل رقم الهاتف مطلوب.',
            'phone.regex' => 'رقم الهاتف يجب أن يبدأ بـ + ومفتاح الدولة.',
            'email.required' => 'حقل البريد الإلكتروني مطلوب.',
            'email.email' => 'البريد الإلكتروني يجب أن يكون عنوان بريد إلكتروني صالح.',
        ];
    }
}
