@charset "UTF-8";

/* body::before {
    display: block;
    content: '';
    height: 60px;
} */

@font-face {
    font-family: 'IBM Plex Sans Arabic';
    src: url('https://addrus.com/public/IBM Plex Sans Arabic.eot');
    src: url('https://addrus.com/public/IBM Plex Sans Arabic.eot?#iefix') format('embedded-opentype'),
        url('https://addrus.com/public/IBM Plex Sans Arabic.woff2') format('woff2'),
        url('https://addrus.com/public/IBM Plex Sans Arabic.woff') format('woff'),
        url('https://addrus.com/public/IBM Plex Sans Arabic.ttf') format('truetype'),
        url('https://addrus.com/public/IBM Plex Sans Arabic.svg#IBM Plex Sans Arabic') format('svg');
}
.card-img-top{
    height: 10rem;
    object-fit: contain;
}

.header-logo {
    width: 10rem;
    object-fit: contain;
}

/* Highlight the selected card */
input[type="radio"]:checked+.card {
    border: 2px solid #0d6efd;
    /* Bootstrap primary color */
    background-color: #f8f9fa;
    /* Light background */
}

.card.active {
    border: 2px solid #0d6efd;
    background-color: #f8f9fa;
}

.card:has(> input[name="plan_id"]:checked) {
    border: 2px solid #0d6efd;
    background-color: #f8f9fa;
}

.our-post-content {
    background: #429b85;
    background: -moz-linear-gradient(45deg, #429b85 0, #5f7b8c 100%);
    background: -webkit-linear-gradient(45deg, #429b85 0, #5f7b8c 100%);
    background: linear-gradient(-250deg, #00395f 0, #006eb7 100%);
    padding: 45px 0;
    position: relative;
    overflow: hidden
}

@media only screen and (min-width:768px) and (max-width:991px) {
    .our-post-content {
        padding-bottom: 0
    }
}

@media only screen and (min-width:480px) and (max-width:767px) {
    .our-post-content {
        padding-bottom: 0
    }
}

@media only screen and (min-width:320px) and (max-width:479px) {
    .our-post-content {
        padding-bottom: 0
    }
}

@media only screen and (min-width:768px) and (max-width:991px) {
    .our-post-content .how-we-work-wrap .col-lg-4 {
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%
    }
}

.our-post-content .hw-circle {
    width: 50px;
    height: 50px;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%;
    position: absolute;
    border: 8px solid #fff;
    opacity: .1
}

.our-post-content .hw-circle:nth-child(1) {
    top: -20px;
    right: 30px
}

.our-post-content .hw-circle:nth-child(2) {
    top: 73%;
    right: 50%
}

.our-post-content .hw-circle:nth-child(3) {
    top: 5px;
    left: 10%
}

.our-post-item {
    display: -webkit-flex;
    display: -ms-flex;
    display: flex;
    -ms-flex-align: center;
    align-items: center
}

@media(max-width:1199px) {
    .our-post-item {
        display: block;
        text-align: center
    }
}

@media only screen and (min-width:768px) and (max-width:991px) {
    .our-post-item {
        margin-bottom: 30px
    }
}

@media only screen and (min-width:480px) and (max-width:767px) {
    .our-post-item {
        margin-bottom: 30px
    }
}

@media only screen and (min-width:320px) and (max-width:479px) {
    .our-post-item {
        margin-bottom: 30px
    }
}

.our-post-item .icon-element {
    font-size: 30px;
    width: 55px;
    height: 55px;
    line-height: 55px;
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1)
}

.our-post-item .widget-title {
    color: #ffc107;
    font-size: 20px
}

.our-post-item .our__text {
    margin-right: 14px
}

@media(max-width:1199px) {
    .our-post-item .our__text {
        margin-right: 0;
        margin-top: 20px
    }
}

.our-post-item:hover .icon-element {
    background-color: #fff;
    color: #0269ac
}

.social-profile li {
    display: inline-block
}

.social-profile li a {
    /* color: #233d63; */
    display: inline-block;
    width: 36px;
    height: 36px;
    /* text-align: center;
    vertical-align: middle;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%;
    background-color: rgba(35, 61, 99, 0.1);
    -webkit-transition: all .3s;
    -moz-transition: all .3s;
    -ms-transition: all .3s;
    -o-transition: all .3s;
    transition: all .3s;
    position: relative;
    z-index: 0; */
}

.social-profile li a:hover {
    color: #fff;
    background-color: #0269ac
}

.breadcrumb-area {
    background-color: #f7fafd;
    /* height: 300px; */
    display: -webkit-flex;
    display: -ms-flex;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    position: relative;
    text-align: center;
    z-index: 2;
    color: #fff;
    background-image: url("../images/breadcrumb-bg.jpg");
    background-size: cover;
    background-position: center
}

/* @media(max-width:375px) {
    .breadcrumb-area {
        height: 220px
    }
} */

.breadcrumb-area:before {
    position: absolute;
    content: '';
    top: 0;
    width: 100%;
    height: 100%;
    background-color: #0269ac;
    opacity: .9;
    z-index: -1
}

.breadcrumb-content {
    position: relative;
    z-index: 3
}

.breadcrumb-content .bread-img-wrap {
    margin-left: 20px;
    width: 130px;
    height: 130px;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%
}

@media(max-width:480px) {
    .breadcrumb-content .bread-img-wrap {
        margin-left: 0;
        margin-bottom: 20px;
        width: 100px;
        height: 100px
    }
}

.breadcrumb-content .bread-img-wrap img {
    width: 100%;
    height: 100%;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%
}

/* .breadcrumb-content .section__title {
    font-size: 60px;
    text-transform: capitalize;
    font-weight: 700;
    color: #fff
}

@media only screen and (min-width:768px) and (max-width:991px) {
    .breadcrumb-content .section__title {
        font-size: 50px
    }
}

@media only screen and (min-width:480px) and (max-width:767px) {
    .breadcrumb-content .section__title {
        font-size: 50px
    }
}

@media only screen and (min-width:320px) and (max-width:479px) {
    .breadcrumb-content .section__title {
        font-size: 40px
    }
} */