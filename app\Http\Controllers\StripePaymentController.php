<?php

namespace App\Http\Controllers;

use Illuminate\Support\Str;
use App\Enums\Currency;
use App\Enums\PaymentProvider;
use App\Enums\TransactionStatus;
use App\Http\Requests\stripeRequest;
use App\Jobs\CheckUsersAndCreate;
use App\Jobs\ProcessAccountData;
use App\Jobs\SendEmail;
use App\Jobs\SendText;
use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Stripe\Stripe;
use Stripe\Checkout\Session;

class StripePaymentController extends Controller
{
    public function __construct()
    {
        Stripe::setApiKey(config('stripe.sk_api'));
    }

    /**
     * Initialize stripe checkout session.
     * - Retrieves the user's data.
     * - Fetches the plan details from the database.
     * - Verifies if a subscription account is available for the selected plan.
     * - Creates a Stripe checkout session for payment.
     * - Returns the payment URL as json.
     * 
     * @param Request $request / The request containing user details and plan selection.
     * @return  JSON response with success or failure status.
     */
    public function initiatePayment(Request $request)
    {

        try {
            $phone = $request["phone"];
            $email = $request["email"] ?? null;
            $plan_id = $request["plan_id"];

            // Retrieve plan details from the database
            $plan = SubscriptionPlan::where('id', $plan_id)->first();;
            if (!$plan) {
                Log::warning('Subscription plan unavailable', ['plan_id' => $plan->id]);
                return response()->json(['status' => false, 'message' => ' الرجاء المحاولة لاحقاً.', 'title' => 'فشل في المعالجة'], 500);
            }

            // Check if the subscription account is available for the selected plan
            $subscription_account = $this->findSubscriptionAccount($plan_id);
            if (!$subscription_account) {
                Log::error('No subscription account found');
                return response()->json(['status' => false, 'message' => 'يرجى التحقق من البيانات المدخلة والمحاولة مرة أخرى. إذا استمرت المشكلة، يرجى التواصل مع الدعم الفني.', 'title' => 'تعذر اتمام العملية'], 500);
            }

            //Create stripe checkout session
            $session = Session::create([
                'payment_method_types' => ['card'],
                'mode' => 'payment',

                // only include customer_email if it is not empty
                ...(!empty($email) ? ['customer_email' => $email] : []),
                'line_items' => [[
                    'price_data' => [
                        'currency' => 'usd',
                        'product_data' => [
                            'name' => "Addrus subscription",
                        ],
                        'unit_amount' => $plan->price * 100,
                    ],
                    'quantity' => 1,
                ]],
                'metadata' => [
                    'phone' => $phone,
                    'plan' => 1,
                    "price" => $plan->price
                ],
                'expires_at' => time() + 3600, //1 hour
                'success_url' => url('/stripe/success/{CHECKOUT_SESSION_ID}'),
                'cancel_url' => url('/'),
            ]);
            Log::info('Stripe session created', ['session_id' => $session->id]);

            DB::transaction(function () use ($session, $subscription_account, $plan, $email, $phone) {
                // Store the transaction details in the database
                $uuid = Str::uuid();

                $newUser = Transaction::create([
                    'subscription_id' => $subscription_account->id,
                    'payment_provider' => PaymentProvider::STRIPE,
                    'amount' => $plan->price,
                    'currency' => Currency::USD,
                    'status' => TransactionStatus::PENDING,
                    'email' => $email ?? "",
                    'phone' => $phone,
                    'uuid' => $uuid,
                    'external_ref' => $session->id

                ]);
                if (!$newUser) {
                    throw new \Exception('Failed to update subscription account status');
                }

                // Update subscription account status
                $subscription_update = $subscription_account->update(['status' => 1]);
                if (!$subscription_update) {
                    throw new \Exception('Failed to update subscription account status');
                }
            });

            //  Return json response for WhatsApp 
            return response()->json([
                'status' => true,
                'url' => $session->url
            ]);
        } catch (\Exception $e) {
            Log::error('Checkout process failed', ['error_message' => $e->getMessage(),]);
            return response()->json(['status' => false, 'message' => 'يرجى التحقق من البيانات المدخلة والمحاولة مرة أخرى. إذا استمرت المشكلة، يرجى التواصل مع الدعم الفني.', 'title' => 'تعذر اتمام العملية'], 500);
        }
    }
    public function checkout(stripeRequest $request)
    {

        try {
            $validatedData = $request->validated();
            $session = $this->initiatePayment(new  Request($validatedData));
            $sessionData = $session->getData(true);

            if ($sessionData['status'] == true) {
                return redirect()->away($sessionData['url']);
            }
            return $this->showFailurePage($sessionData['title'], $sessionData['message']);
        } catch (\Exception $e) {
            Log::error('Checkout process failed', ['error_message' => $e->getMessage(),]);
            return $this->showFailurePage('تعذر اتمام العملية', 'يرجى التحقق من البيانات المدخلة والمحاولة مرة أخرى. إذا استمرت المشكلة، يرجى التواصل مع الدعم الفني.');
        }
    }


    /**
     * Verifies the payment status using a Stripe session ID.
     * If the payment is successful, it retrieves the user's subscription details.
     */
    public function verifyPayment($session_id)
    {
        // Validate the session ID
        if (!$session_id) {
            Log::warning('Session ID is missing in verifyPayment');
            return $this->showFailurePage('معرف الجلسة غير موجود', 'عذراً، معرف الجلسة غير موجود. الرجاء المحاولة مرة أخرى.');
        }
        try {

            // Retrieve payment details from Stripe
            $session = Session::retrieve($session_id);

            // Check if the payment was successful
            if ($session->payment_status === 'paid') {

                $subscription_account = $this->handlePaymentIntentSucceeded($session);
                //Check if subscription account is available to send it to the user
                if (!$subscription_account) {
                    Log::error('Subscription not found', ['session_id' => $session_id]);
                    return $this->showFailurePage('تعذر اتمام العملية', ' تم الدفع بنجاح.ولكن حدث خطأ في عملية الاشتراك يرجى الاتصال بخدمة العملاء على الرقم :');
                }

                CheckUsersAndCreate::dispatch();
                $email = $subscription_account->subscription->email;
                $password = $subscription_account->subscription->password;
                return view('international.reciept', compact('email', 'password'));
            }

            Log::error('error in payment status', ['status' => $session->payment_status]);
            return $this->showFailurePage('فشلت عملية الدفع', ' الرجاء التحقق من تفاصيل الدفع أو المحاولة مرة أخرى.');
        } catch (\Exception $e) {
            Log::error('Error verifying payment', [
                'session_id' => $session_id,
                'error_message' => $e->getMessage(),
            ]);
            return response()->json(['error' => 'Error verifying payment' . $e], 500);
        }
    }

    /**
     * Handle incoming Stripe webhook events
     * - checkout.session.completed => Triggered when the checkout session is completed.
     * - Check the payment_status to determine if the payment was successful paid, failed unpaid. 
     */
    public function webhook()
    {
        $endpoint_secret = config('stripe.stripe_webhook_secret');
        $payload = @file_get_contents('php://input');
        $sig_header = request()->header('Stripe-Signature');
        $event = null;

        try {
            // Verify the webhook signature and construct the event
            $event = \Stripe\webhook::constructEvent(
                $payload,
                $sig_header,
                $endpoint_secret
            );
            // Log the event type
            Log::info('Stripe webhook event constructed', ['event_type' => $event->type]);

            // Process the event based on its type
            switch ($event->type) {

                case 'checkout.session.completed':
                    // Triggered when a checkout session is successfully completed
                    $paymentIntent = $event->data->object;
                    if ($paymentIntent->payment_status === 'paid') {
                        // Payment was successful
                        $this->handlePaymentIntentSucceeded($paymentIntent);
                        Log::info('Payment successful for session');
                    } else {
                        // Payment failed or was incomplete
                        $this->handlePaymentIntentfailed($paymentIntent);
                        Log::warning('Payment incomplete or failed for session');
                    }
                    Log::info('Processed checkout.session.completed', ['payment_intent' => $paymentIntent]);
                    break;

                case 'payment_intent.payment_failed':
                    // Triggered when a payment fails
                    $paymentMethod = $event->data->object;
                    $sessions = Session::all([
                        'payment_intent' => $paymentMethod->id,
                        'limit' => 1
                    ]);

                    if (!empty($sessions->data)) {
                        $session = $sessions->data[0];
                        $this->handlePaymentIntentfailed($session);
                        Log::info("Checkout Session : " . $session);
                    } else {
                        Log::warning("No session found for Payment Intent: ");
                    }
                    break;

                case 'checkout.session.expired':
                    // Triggered when a checkout session expires
                    $session = $event->data->object;
                    $this->handlePaymentIntentCanceled($session->id);
                    break;

                default:
                    Log::info('Received unknown event type', ['event' => $event->typet]);
            }
            http_response_code(200);
        } catch (\Exception $e) {
            http_response_code(400);
            Log::error('Webhook processing failed', ['error' => $e->getMessage()]);
            return response()->json(['error' => 'Webhook processing failed'], 500);
        }
    }

    private function handlePaymentIntentSucceeded($session)
    {
        try {
            return DB::transaction(function () use ($session) {
                //Get session id
                $session_id = $session->id;
                // Ensure the transaction exists and has not already been marked as paid
                $transaction = Transaction::where('external_ref', $session_id)->with('subscription')->first();

                // If transaction is not exists 
                if (!$transaction) {
                    Log::info('transaction  ', ['transaction is not exists']);

                    // Get subscription account
                    $subscription_account = $this->findSubscriptionAccount($session->metadata->plan);
                    if (!$subscription_account) {
                        Log::error('No subscription account found and can not be cearted.');
                        return null;
                    }
                    $uuid = Str::uuid();
                    //Insert data in transaction table
                    $newUser = Transaction::create(
                        [
                            'subscription_id' => $subscription_account->id,
                            'payment_provider' => PaymentProvider::STRIPE,
                            'amount' => $session->metadata->price,
                            'currency' => Currency::USD,
                            'status' => TransactionStatus::COMPLETED,
                            'uuid' => $uuid,
                            'external_ref' => $session_id,
                            'email' => $session->customer_email,
                            'phone' => $session->metadata->phone
                        ]
                    );
                    if (!$newUser) {
                        Log::error('Failed  ', ['Failed to insert user']);
                        return null;
                    }
                    // Update subscription_account status 
                    $subscription_account->update(['status' => 1]);
                    $transaction = Transaction::where('external_ref', $session_id)->with('subscription')->first();
                    if ($transaction->email != '') {
                        SendEmail::dispatch($transaction);
                    }
                    // Send subscription via whatsApp
                    $accountData = [];
                    $accountData[] = $transaction->subscription->email;
                    $accountData[] = $transaction->subscription->password;
                    SendText::dispatch($transaction->phone, 'sendAccountData', $accountData);
                    return $newUser;
                }
                ////////////

                //If transaction is exsit and the status is COMPLETED
                if ($transaction->status === TransactionStatus::COMPLETED) {
                    return $transaction; // Return the user if the transaction is already completed 
                }
                ////////////
                if ($transaction->status === TransactionStatus::FAILED) {
                    Log::info(['transaction is exsit but the status is TransactionStatus::FAILED']);

                    $subscription_account = Subscription::where("id", $transaction->subscription_id)->where('status', 0)->first();
                    if (!$subscription_account) {
                        $subscription_account = $this->findSubscriptionAccount($session->metadata->plan);
                        if (!$subscription_account) {
                            Log::error('No subscription account found and can not be cearted.');
                            return  null;
                        }
                        $transaction->update(['subscription_id' => $subscription_account->id]);
                        $subscription_account->update(['status' => 1]);
                    }
                }

                //If transaction is exsit but the status is pending
                $transaction->update(['status' => TransactionStatus::COMPLETED,]);

                // Send subscription via email
                if ($transaction->email != '') {
                    SendEmail::dispatch($transaction);
                }
                // Send subscription via whatsApp
                $accountData = [];
                $accountData[] = $transaction->subscription->email;
                $accountData[] = $transaction->subscription->password;
                SendText::dispatch($transaction->phone, 'sendAccountData', $accountData);
                return $transaction;
            });
        } catch (\Exception $e) {
            Log::error('Failed  ', ['Failed to handle payment intent succeeded' => $e->getMessage()]);
        }
    }

    private function handlePaymentIntentfailed($session)
    {
        try {
            DB::transaction(function () use ($session) {
                //Get session id
                $session_id = $session->id;
                $transaction =  Transaction::where('external_ref', $session_id)->first();
                // Ensure the transaction is exists and has not already been marked as failed

                if ($transaction && $transaction->status === TransactionStatus::PENDING) {
                    // Update transaction status to failed
                    $transaction->update(["status" => TransactionStatus::FAILED]);
                    Subscription::where('id', $transaction->subscription_id)->update([
                        'status' => 0
                    ]);
                }
            });
        } catch (\Exception $e) {
            Log::error('Failed  ', ['error' => $e->getMessage()]);
        }
    }

    private function handlePaymentIntentCanceled($session_id)
    {
        try {
            DB::transaction(function () use ($session_id) {
                $transaction = Transaction::where('external_ref', $session_id)->first();
                if (!$transaction) {
                    return;
                }
                // Mark the transaction as canceled
                $transaction->update(['status' =>  TransactionStatus::CANCELED]);
                Subscription::where('id', $transaction->subscription_id)->update([
                    'status' => 0
                ]);
            });
        } catch (\Exception $e) {
            Log::error("Error updating canceled transaction: " . $e->getMessage());
        }
    }


    public function findSubscriptionAccount($plan_id)
    {
        // Check if the subscription account is available for the selected plan
        $subscription_account = Subscription::where("status", 0)
            ->where('subscription_plan_id', $plan_id)
            ->first();

        if (!$subscription_account) {

            $plan = SubscriptionPlan::where('id', $plan_id)->first();;
            //Ganerate account from API 
            ProcessAccountData::dispatchSync(['number' => 1, 'number_days' => $plan->period, 'plan' => $plan_id, "job_process" => 0]);
            $subscription_account = Subscription::where("status", 0)
                ->where('subscription_plan_id', $plan_id)
                ->first();
        }
        return $subscription_account;
    }

    public function showFailurePage($error, $message)
    {
        return view('international.error', compact('error', 'message'));
    }
}
