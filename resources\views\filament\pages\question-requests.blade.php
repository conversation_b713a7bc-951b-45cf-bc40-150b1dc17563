<x-filament::page>
    <div class="space-y-4">
        @forelse ($questions as $question)
        <div class="bg-white border rounded p-4 shadow-sm">
            <div class="flex justify-between items-center flex-wrap gap-4">
                {{-- Left: Info --}}
                <div class="text-sm text-gray-700 space-y-1">
                    <div>
                        {{ __('filament-panels.question.fields.name') }}:
                        <strong>{{ $question->student->name ?? 'Unknown' }}</strong>
                    </div>
                    <x-filament::badge color="warning" class="text-sm">
                        {{ $question->grade->name ?? '—' }} / {{ $question->subject->name ?? '—' }}
                    </x-filament::badge>

                    <div class="text-xs text-gray-400">
                        {{ $question->created_at->format('Y-m-d H:i') }}
                    </div>
                </div>

                {{-- Right: Button --}}
                <x-filament::button
                href="{{ url('/tutor/view-and-accept-question/' . $question->id) }}"

                    color="primary"
                    size="sm"
                    tag="a">
                   {{__('filament-panels.question.action.view')}}
                </x-filament::button>
            </div>
        </div>
        @empty
        <p class="text-center text-gray-400">  {{__('filament-panels.question_request.body.no_request')}}</p>
        @endforelse
    </div>
</x-filament::page>