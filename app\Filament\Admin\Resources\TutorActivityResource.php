<?php

namespace App\Filament\Admin\Resources;

use App\Enums\TutorActivities;
use App\Filament\Admin\Resources\TutorActivityResource\Pages;
use App\Models\AskMe\TutorActivity;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

/**
 * Tutor Activity Resource
 *
 * Manages and displays tutor activity logs in the admin panel.
 * Provides functionality to view and filter activities performed by tutors,
 */
class TutorActivityResource extends Resource
{
    /**
     * The model that this resource manages
     */
    protected static ?string $model = TutorActivity::class;
    protected static ?string $navigationIcon = 'heroicon-o-clock';

    /**
     * Get the title for this resource page
     *
     * @return string The localized title
     */
    public function getTitle(): string
    {
        return __('filament-panels.tutor_activities.plural');
    }

    /**
     * Get the singular label for this resource
     *
     * @return string The localized singular label
     */
    public static function getModelLabel(): string
    {
        return __('filament-panels.tutor_activities.singular');
    }

    /**
     * Get the plural label for this resource
     *
     * @return string The localized plural label
     */
    public static function getPluralModelLabel(): string
    {
        return __('filament-panels.tutor_activities.plural');
    }

    /**
     * Get the navigation label for this resource
     *
     * @return string The localized navigation label
     */
    public static function getNavigationLabel(): string
    {
        return __('filament-panels.tutor_activities.title');
    }


    /**
     *
     *This resource primarily uses the view-only mode, so the form is empty
     *
     * @param Form $form The Filament form instance
     * @return Form The configured form
     */
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
            ]);
    }

    /**
     * Define the table structure for displaying tutor activities
     *
     * @param Table $table The Filament table instance
     * @return Table The configured table
     */
    public static function table(Table $table): Table
    {
        return $table->columns([
            // Tutor name column
            TextColumn::make('tutor.user.name')
                ->label(__('filament-panels.tutor.fields.name'))
                ->searchable(),

            // Student name column (who asked the question)
            TextColumn::make('question.student.name')
                ->label(__('filament-panels.question.fields.name'))
                ->searchable(),

            // Student phone number column
            TextColumn::make('question.student.number')
                ->label(__('filament-panels.tutor.fields.phone'))
                ->searchable(),

            // Question ID column
            TextColumn::make('question.id')
                ->label(__('filament-panels.question.fields.id')),

            // Activity type column with color-coded badges
            TextColumn::make('action')
                ->label(__('filament-panels.tutor_activities.singular'))
                ->badge()
                ->formatStateUsing(fn(string $state) => TutorActivities::from($state)->label())
                // Set the badge color based on the activity type
                ->color(fn(string $state) => TutorActivities::from($state)->color()),

            // Timestamp column showing when the activity occurred
            TextColumn::make('created_at')
                ->dateTime()
                ->label(__('filament-panels.question.fields.created_at')),
        ])
        ->filters([
            // Filter for activity type
            SelectFilter::make('action')
                ->label(__('filament-panels.tutor_activities.singular'))
                ->options([
                    // Options from the TutorActivities enum
                    TutorActivities::Accept->value => TutorActivities::Accept->label(),
                    TutorActivities::Answer->value => TutorActivities::Answer->label(),
                    TutorActivities::Edit->value   => TutorActivities::Edit->label(),
                ])
                ->attribute('action')
                ->default(null),
        ]);
    }


    public static function getRelations(): array
    {
        return [
        ];
    }

    /**
     * Define the pages for the Tutor Activity resource
     *
     * @return array The pages configuration
     */
    public static function getPages(): array
    {
        return [
            // Only the index page is needed for this resource
            'index' => Pages\ListTutorActivities::route('/'),
        ];
    }
}
