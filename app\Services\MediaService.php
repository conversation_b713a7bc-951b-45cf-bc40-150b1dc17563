<?php
namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class MediaService
{
  
    /**
     * Download and save a media file from WhatsApp by its media ID.
     *
     * @param string $mediaId The ID of the media from WhatsApp
     * @param string $folder The target folder to save the file in (default: 'questions')
     * @return string|null The public URL of the saved file, or null on failure
     */
    public function getMediaAndSave(string $mediaId, string $folder = 'questions'): ?string
    {
        try {
            $token = config('api.whatsapp_access_token');
            $apiUrl = config('api.whatsapp_api_url') . "/{$mediaId}";

            $mediaResponse = Http::withToken($token)->get($apiUrl);
            if (!$mediaResponse->successful()) {
                return null;
            }

            $mediaUrl = $mediaResponse->json()['url'];

            //Download the actual media file
            $fileResponse = Http::withToken($token)->get($mediaUrl);
            if (!$fileResponse->successful()) {
                return null;
            }

            //Determine file extension based on MIME type
            $mime = $fileResponse->header('Content-Type');
            $ext = match ($mime) {
                'image/png'        => 'png',
                'image/jpeg'       => 'jpg',
                'video/mp4'        => 'mp4',
                'application/pdf'  => 'pdf',
                default            => 'bin',
            };

            //Generate unique filename and save the file to public storage
            $filename = "{$folder}/" . uniqid('whatsapp_', true) . ".{$ext}";
            Storage::disk('public')->put($filename, $fileResponse->body());

            //Return the public URL to the saved file
            return Storage::url($filename);
        } catch (\Exception $e) {
            Log::error('WhatsApp Media Save Error', ['message' => $e->getMessage()]);
            return null;
        }
    }
}
