<?php

namespace App\Filament\Admin\Widgets;

use App\Enums\TransactionStatus;
use App\Models\AskMe\Transaction;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\Log;

/**
 * TransactionStatusPieChart Widget
 *
 * This widget displays a pie chart  of transactions grouped by their status.
 */
class TransactionStatusPieChart extends ChartWidget
{

    protected static ?string $heading = 'إحصائيات حالة المعاملات المدفوعة والمعلقة والفاشلة';
    protected static ?int $sort = 4;
    protected static ?string $maxHeight = '300px';

    /**
     * Gather data for the pie chart
     *
     * This method:
     * - Retrieves all transaction statuses from the enum
     * - Counts transactions for each status
     * - Collects labels, counts, and colors for each status
     *
     * @return array Chart data structure with datasets and labels
     */
    protected function getData(): array
    {
        // Get all possible transaction status enum cases
        $statuses = TransactionStatus::cases();

        // Initialize arrays to store chart data
        $labels = []; 
        $data = [];  
        $colors = [];

        foreach ($statuses as $status) {
            // Count transactions with the current status
            $count = Transaction::where('status', $status->value)->count();

            // Add the status label to the labels array for chart display
            $labels[] = $status->label();

            // Add the count of transactions with this status to the data array
            $data[] = $count;

            // Add the status-specific color to the colors array for chart styling
            $colors[] = $status->color();
        }

        return [
            'datasets' => [
                [
                    // Set the dataset label using localized text from translation files
                    'label' => __('filament-panels.dashboard.transaction_status'),

                    // Transaction counts for each status
                    'data' => $data,

                    // Status-specific colors for chart segments
                    'backgroundColor' => $colors,
                ],
            ],
            // Labels for each chart segment
            'labels' => $labels,
        ];
    }
    protected function getType(): string
    {
        return 'pie';
    }
}
