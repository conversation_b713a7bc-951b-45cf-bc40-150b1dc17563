<?php

namespace App\Filament\Admin\Widgets;

use App\Enums\TransactionStatus;
use App\Models\AskMe\Transaction;
use Filament\Widgets\ChartWidget;

/**
 * Revenue Chart Widget
 *
 * Displays a line chart of revenue data with filtering options.
 */
class RevenueChart extends ChartWidget
{

    protected static ?string $heading = 'إحصائيات الإيرادات';
    protected static ?int $sort = 2;
    protected int | string | array $columnSpan = 'full';
    public ?string $filter = 'month';

    /**
     * Define the available time period filters for the chart
     *     *
     * @return array|null The filters configuration
     */
    protected function getFilters(): ?array
    {
        return [
            // Weekly view (shows data for each day of the current week)
            'week' => __('filament-panels.dashboard.range_week'),
            // Monthly view (shows data for each day of the current month)
            'month' => __('filament-panels.dashboard.range_month'),
            // Yearly view (shows data for each month of the current year)
            'year' => __('filament-panels.dashboard.range_year'),
        ];
    }
    /**
     * Generate the data for the revenue chart
     *
     * This method calculates revenue data based on the selected time period filter
     * and formats it for display in the chart.
     *
     * @return array The chart data configuration
     */
    protected function getData(): array
    {
        // Get the current filter or use 'month' as default
        $filter = $this->filter ?? 'month';
        $labels = [];
        $data = [];

        // Generate data for weekly view 
        if ($filter === 'week') {
            $start = now()->startOfWeek();
            $end = now()->endOfWeek();

            // Loop through each day of the week
            for ($i = 0; $i <= 6; $i++) {
                $day = $start->copy()->addDays($i);
                // Add day name to labels 
                $labels[] = __($day->translatedFormat('l'));

                // Calculate total revenue for this day
                $dailyTotal = Transaction::where('status', TransactionStatus::COMPLETED)
                    ->whereDate('created_at', $day)
                    ->sum('amount');
                $data[] = round($dailyTotal, 2);
            }
        }
        // Generate data for monthly view 
        elseif ($filter === 'month') {
            $start = now()->startOfMonth();
            $end = now()->startOfDay();

            // Loop through each day from the start of the month to today
            for ($date = $start->copy(); $date->lte($end); $date->addDay()) {
                // Add day and month abbreviation to labels 
                $labels[] = $date->translatedFormat('d M');

                // Calculate total revenue for this day
                $dailyTotal = Transaction::where('status', TransactionStatus::COMPLETED)
                    ->whereDate('created_at', $date)
                    ->sum('amount');
                $data[] = round($dailyTotal, 2);
            }
        }
        // Generate data for yearly view
        elseif ($filter === 'year') {
            // Loop through the last 12 months
            for ($i = 11; $i >= 0; $i--) {
                $month = now()->subMonths($i)->startOfMonth();
                // Add month name to labels 
                $labels[] = $month->translatedFormat('F');

                // Calculate total revenue for this month
                $monthlyTotal = Transaction::where('status', TransactionStatus::COMPLETED)
                    ->whereYear('created_at', $month->year)
                    ->whereMonth('created_at', $month->month)
                    ->sum('amount');
                $data[] = round($monthlyTotal, 2);
            }
        }

        return [
            'datasets' => [
                [
                    // Dataset configuration
                    'label' => __('filament-panels.dashboard.revenue'),
                    'data' => $data,
                    'borderColor' => '#3b82f6', // Blue line color
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)', // Light blue fill
                    'fill' => true, // Fill area under the line
                    'tension' => 0.4, 
                ],
            ],
            'labels' => $labels,
        ];
    }


    protected function getType(): string
    {
        return 'line';
    }
}
