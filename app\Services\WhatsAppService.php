<?php

namespace App\Services;

use App\Jobs\SendWhatsAppPayment;
use Illuminate\Support\Facades\Log;

class WhatsAppService
{
    public function sendText($to, $message, $provider = 'AskMe')
    {
        $payload = [
            'messaging_product' => 'whatsapp',
            'to' => $to,
            'type' => 'text',
            'text' => [
                'body' => $message
            ]
        ];

        SendWhatsAppPayment::dispatch($payload, $provider);
    }

    public function sendList($to, $bodyText, $buttonText, $sections, $provider = 'AskMe')
    {
        $payload = [
            'messaging_product' => 'whatsapp',
            'to' => $to,
            'type' => 'interactive',
            'interactive' => [
                'type' => 'list',
                'body' => ['text' => $bodyText],
                'action' => [
                    'button' => $buttonText,
                    'sections' => $sections
                ]
            ]
        ];

        SendWhatsAppPayment::dispatch($payload, $provider);
    }

    public function sendButtons($to, $bodyText, $buttons, $provider = 'AskMe')
    {
        Log::info("buttons: ", [$buttons]);
        $payload = [
            'messaging_product' => 'whatsapp',
            'to' => $to,
            'type' => 'interactive',
            'interactive' => [
                'type' => 'button',
                'body' => ['text' => $bodyText],
                'action' => ['buttons' => $buttons]
            ]
        ];

        SendWhatsAppPayment::dispatch($payload, $provider);
    }
    public function sendTemplate($to, $templateName, $languageCode = 'ar', $components, $provider = 'AskMe')
    {
        $payload = [
            'messaging_product' => 'whatsapp',
            'to' => $to,
            'type' => 'template',
            'template' => [
                'name' => $templateName,
                'language' => [
                    'code' => $languageCode
                ],
                'components' => $components
            ]
        ];
        Log::info("payload: ", [$payload]);

        SendWhatsAppPayment::dispatch($payload, $provider);
    }

    public function sendImage($to, $link, $caption = null, $provider = 'AskMe')
    {
        $payload = [
            'messaging_product' => 'whatsapp',
            'to' => $to,
            'type' => 'image',
            'image' => [
                'link' => $link,
                'caption' => $caption,
            ]

        ];

        SendWhatsAppPayment::dispatch($payload, $provider);
    }

    public function sendVideo(string $to, string $link, $caption = null, $provider = 'AskMe'): void
    {
        try {
            $payload = [
                "messaging_product" => "whatsapp",
                "to" => $to,
                "type" => "video",
                "video" => [
                    "link" => $link,
                    "caption" => $caption,
                ]
            ];

            SendWhatsAppPayment::dispatch($payload, $provider);
        } catch (\Exception $e) {
            Log::error("Failed to send WhatsApp video", ['error' => $e->getMessage()]);
        }
    }
    public function sendDocument(string $to, string $fileUrl, $caption = null, $provider = 'AskMe'): void
{
    try {
        $payload = [
            "messaging_product" => "whatsapp",
            "to" => $to,
            "type" => "document",
            "document" => [
                "link" => $fileUrl,
                "caption" => $caption,
            ]
        ];

        SendWhatsAppPayment::dispatch($payload, $provider);
    } catch (\Exception $e) {
        Log::error("Failed to send WhatsApp document", ['error' => $e->getMessage()]);
    }
}

}
