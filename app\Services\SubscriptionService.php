<?php

namespace App\Services;

use App\Enums\SubscriptionPlanType;
use App\Enums\WhatsAppMessageType;
use App\Models\AskMe\Setting;
use App\Models\AskMe\Student;
use App\Models\AskMe\Subscription;
use App\Models\AskMe\SubscriptionPackage;
use App\Models\AskMe\WhatsAppMessages;
use Illuminate\Support\Facades\Log;

class SubscriptionService
{
    protected  $whatsAppService;

    public function __construct(
        WhatsAppService $whatsAppService,
    ) {
        $this->whatsAppService = $whatsAppService;
    }


    /**
     * Send a welcome message to the user based on their subscription status.
     *
     * This checks if the student exists, creates them if not, and sends the message:
     * - Trial message if subscriptions are not required.
     * - Subscription-based message if required and the user has an active one.
     * - General prompt to subscribe if required and no subscription exists.
     *
     * @param string $to The WhatsApp number of the student.
     */
    public function sendWelcomeMessage(string $to, string $name): void
    {
        Log::info("Send Welcome Message to: " . $to);
        try {
            // Check if the student exists by phone number
            $student = Student::where('number', $to)->first();

            // If not found, create a student record
            if (!$student) {
                $student = Student::create(['number' => $to, 'name' => $name]);
            }

            // Check if the student has an active subscription 
            $activeSubscription = $student->subscriptions()
                ->whereRaw('total_questions > used_questions')
                ->latest()
                ->exists();

            // Check system setting is a subscription required to ask questions
            $isSubscriptionRequier =(int)Setting::get('is_subscription_required') ?? 0;

            // type of welcome message to send
            match (true) {
                // Trial mode
                $isSubscriptionRequier === 0 => $this->sendTrialWelcomeMessage($to, $student),

                // Subscribed user
                $isSubscriptionRequier === 1 && $activeSubscription => $this->sendSubscriptionWelcomeMessage($to, $student),

                // Not subscribed
                $isSubscriptionRequier === 1 && !$activeSubscription => $this->sendGeneralWelcomeMessage($to),

                default => null
            };
        } catch (\Exception $error) {
            Log::error('Welcome Message Error: ' . $error->getMessage());
        }
    }


    /**
     * Sends a welcome message to students in trial mode .
     *
     * The message includes options to:
     * - Ask a new question
     * - View previous questions 
     *
     * @param string $to The student's WhatsApp number.
     * @param Student $student The student model instance.
     */
    private function sendTrialWelcomeMessage(string $to, Student $student): void
    {
        //  Get the pre-defined WhatsApp message for trial welcome
        $whatsAppWelcomeMessagesSubscription = WhatsAppMessages::where('type', [WhatsAppMessageType::WELCOME_TRIAL])->first();

        // Build interactive reply buttons
        $row = [
            [
                "type" => "reply",
                "reply" => [
                    "id" => "ask_question",
                    "title" => 'طرح سؤال جديد'
                ]
            ],
            [
                "type" => "reply",
                "reply" => [
                    'id' => 'view_grades_subjects',
                    'title' => 'عرض الصفوف والمواد'
                ],

            ]
        ];

        //  Add a View My Questions button if the student has asked any before
        if ($student->questions()->count() > 0) {
            $row[] = [
                "type" => "reply",
                "reply" => [
                    "id" => "view_questions",
                    "title" => 'عرض اسئلتي السابقة'
                ],
            ];
        }

        $message = empty($whatsAppWelcomeMessagesSubscription->message)
            ? 'مرحبًا بك في خدمة اسألني. خدماتنا مخصصة للإجابة على أسئلتك وتقديم المعلومات المفيدة.':
            $whatsAppWelcomeMessagesSubscription->message;

        // Send the message with interactive buttons
        $this->whatsAppService->sendButtons($to, $message, $row);
    }

    /**
     * Sends a welcome message for subscribed students.
     *
     * Displays a list of options including:
     * - Asking a new question
     * - Viewing current subscription info
     * - Viewing subscription history
     * - Viewing previously asked questions (if available)
     *
     * @param string $to The student's WhatsApp number.
     * @param Student $student The student model instance.
     */
    private function sendSubscriptionWelcomeMessage(string $to, Student $student): void
    {
        //Get the configured welcome message for subscribed users
        $whatsAppWelcomeMessagesSubscription = WhatsAppMessages::where('type', [WhatsAppMessageType::WELCOME_SUBSCRIPTION])->first();

        //Build the interactive menu sections
        $sections = [
            [
                'title' => 'القائمة للطلاب المشتركين',
                'rows' => [
                    ['id' => 'ask_question', 'title' => 'طرح سؤال جديد'],
                    ['id' => 'subscription_info', 'title' => 'معلومات الاشتراك الحالي'],
                    ['id' => 'subscription_history', 'title' => 'سجل الاشتراكات'],
                    ['id' => 'view_grades_subjects', 'title' => 'عرض الصفوف والمواد'],

                ]
            ]
        ];

        // Add view questions if student has any
        if ($student->questions()->count() > 0) {
            $sections[0]['rows'][] = ['id' => 'view_questions', 'title' => 'عرض اسئلتي السابقة'];
        }

        $message = empty($whatsAppWelcomeMessagesSubscription->message)
            ? 'مرحبًا بك في خدماتنا! يمكنك اختيار أحد الخيارات من القائمة أدناه. خدماتنا مخصصة للإجابة على أسئلتك وتقديم المعلومات المفيدة.':
            $whatsAppWelcomeMessagesSubscription->message;

        // Send list message to the student
        $this->whatsAppService->sendList($to, $message, "عرض القائمة", $sections);
    }

    /**
     * Sends a general welcome message for users who are not yet subscribed.
     *
     * Encourages the user to subscribe and explore available content options.
     * Includes:
     * - Start subscription
     * - View available subjects
     * - View previous questions
     * - View subscription history
     *
     * @param string $to The user's WhatsApp number.
     */
    private function sendGeneralWelcomeMessage(string $to): void
    {
        Log::info("Send General Welcome Message to: " . $to);
        // Fetch default general welcome message
        $whatsAppWelcomeMessages = WhatsAppMessages::where('type', [WhatsAppMessageType::WELCOME])->first();

        // Build options for unsubscribed users
        $sections = [
            [
                'title' => 'ابدأ معنا',
                'rows' => [
                    ['id' => 'subscribe_now', 'title' => 'اشترك الآن'],
                    ['id' => 'view_grades_subjects', 'title' => 'عرض الصفوف والمواد'],
                    ['id' => 'view_questions', 'title' => 'عرض الأسئلة السابقة'],
                    ['id' => 'subscription_history', 'title' => 'سجل الاشتراكات'],
                ]
            ]
        ];

        $message = empty($whatsAppWelcomeMessages->message)
            ? 'مرحبًا بك في خدماتنا! إذا كنت ترغب في تجربة منصتنا، يرجى الاشتراك للاستفادة من خدماتنا. يرجى اختيار خيار الاشتراك من القائمة أدناه.':
            $whatsAppWelcomeMessages->message;
        Log::info('selections: ' . json_encode($sections));

        // Send interactive menu
        $this->whatsAppService->sendList($to, $message, "عرض القائمة", $sections);
    }


    /**
     * Sends details about the user's currently active subscription.
     *
     * If the user has an active subscription with remaining questions,
     * it shows the plan name, remaining questions, and subscription date.
     *
     * @param string $to The user's WhatsApp number.
     */
    public function sendSubscriptionInfo(string $to): void
    {
        $student = Student::where('number', $to)->first();

        $activeSubscription = $student?->subscriptions()
            ->whereRaw('total_questions > used_questions')
            ->latest()
            ->first();

        if ($activeSubscription) {
            $message = "معلومات الاشتراك الحالي:\n" .
                "الاشتراك: " . $activeSubscription->package->name . "\n" .
                "عدد الأسئلة المتبقية: " . ($activeSubscription->total_questions - $activeSubscription->used_questions) . "\n" .
                "تاريخ الاشتراك: " . $activeSubscription->created_at->format('Y-m-d H:i:s');

            $this->whatsAppService->sendText($to, $message);
        } else {
            $this->whatsAppService->sendText($to, 'لا يوجد اشتراك نشط حاليًا.');
        }
    }

    /**
     * Sends a list of available subscription plans to the user.
     *
     * Triggered when the user selects Subscribe Now from WhatsApp.
     * Each subscription plan includes:
     * - The name of the plan
     * - The number of questions allowed
     * - The price in LYD
     *
     * Plans are retrieved from the database and sent using WhatsApp interactive list.
     *
     * @param string $to The student WhatsApp phone number      */

    public function subscribeNow(string $to): void
    {
        // Retrieve all active subscription plans from the database
        $plans = SubscriptionPackage::where('is_active', true)->where('type', SubscriptionPlanType::LOCAL)->get();

        // If no plans are available
        if ($plans->isEmpty()) {
            $this->whatsAppService->sendText($to, 'لا توجد باقات متاحة حالياً. الرجاء المحاولة لاحقاً.');
            return;
        }

        // Format each plan into a WhatsApp list item
        $rows = $plans->map(function ($plan) {
            return [
                "id" => "plan_" . $plan->id,
                "title" => $plan->name,
                "description" => "عدد الأسئلة: {$plan->question_limit} - السعر: {$plan->price} دينار"
            ];
        })->toArray();

        $selections = [
            [
                "title" => "اختر الباقة",
                "rows" => $rows
            ]
        ];

        $this->whatsAppService->sendList(
            $to,
            "*اختر الباقة المناسبة للاشتراك في خدمة اسألني*",
            "عرض الباقات",
            $selections
        );
    }


    /**
     * Sends the student subscription history .
     *
     * This lists all previous subscriptions, each showing:
     * - Package name
     * - Used/total questions
     * - Subscription date
     *
     * If there are more than 10, the list is paginated into chunks.
     *
     * @param string $to The student number.
     */
    public function sendSubscriptionHistory(string $to): void
    {
        $student = Student::where('number', $to)->first();

        //If student does not exist, notify the user
        if (!$student) {
            $this->whatsAppService->sendText($to, 'لم يتم العثور على بيانات المستخدم. تأكد من أنك مسجل لدينا.');
            return;
        }

        //Get all subscriptions for the student
        $subscriptions = Subscription::with('package')
            ->where('student_id', $student->id)
            ->orderBy('created_at', 'desc')
            ->get();

        // If the student has no subscriptions, notify them
        if ($subscriptions->isEmpty()) {
            $this->whatsAppService->sendText($to, 'ليس لديك أي اشتراكات سابقة.');
            return;
        }

        //Chunk the subscriptions into groups of 10 (WhatsApp limit)
        $chunks = $subscriptions->chunk(10);
        $part = 1;

        foreach ($chunks as $chunk) {
            $rows = [];

            foreach ($chunk as $sub) {
                $rows[] = [
                    'id' => 'plan_' . $sub->id,
                    'title' => $sub->package->name,
                    'description' => "الأسئلة المستخدمة: {$sub->used_questions}/{$sub->total_questions} - {$sub->created_at->format('Y-m-d')}"
                ];
            }

            $this->whatsAppService->sendList(
                $to,
                " *سجل الاشتراكات الخاصة بك* (جزء {$part})",
                "عرض الاشتراكات",
                [[
                    'title' => "الاشتراكات السابقة",
                    'rows' => $rows
                ]]
            );

            $part++;
        }
    }

    /**
     * Handles the user subscription plan selection and initiates the payment process.
     *
     * 1- Sends a processing payment message to the user.
     * 2- Retrieves the selected plan from the database.
     * 3- Determines if the payment is international or local.
     * 4- Calls the payment method and generates a payment url.
     * 5- Sends the payment link to the user via WhatsApp.
     * 
     */
    private function handleSubscriptionSelection($to, $planId)
    {
        try {


            //Extract the actual plan ID remove plan_ prefix
            $planId = str_replace("plan_", "", $planId);
            // Fetch the plan from the database
            $plan = SubscriptionPackage::where('id', $planId)->first();

            // If the plan doesn't exist, notify the user

            if (!$plan) {
                $this->whatsAppService->sendText($to, 'الباقة المحددة غير موجودة.');
                return;
            }

            $this->whatsAppService->sendText($to, "جاري معالجة طلب الدفع الخاص بك... \nيرجى الانتظار لبضع ثوانٍ.");

            //Determine the payment method based on the plan type
            $paymentResponse = null;
            $validatedData = [
                "phone" => '+' . $to,
                "plan_id" => (int) $planId
            ];
            if ($plan->type == SubscriptionPlanType::INTERNATIONAL) {
                // Initialize Stripe payment process
                // $stripeController = new StripePaymentController();

                // $paymentResponse = $stripeController->initiatePayment(new  Request($validatedData));
            } else {
                // Initialize Tlync payment process
                // $paymentResponse = $this->initiateTlyncPayment(new Request($validatedData));
            }

            // $paymentData = $paymentResponse->getData(true);
            // Log::info('paymet', $paymentData);
            // Check if the payment process failed
            if (!isset($paymentData['status']) || $paymentData['status'] === false) {

                $message = "لايمكن اتمام عملية الدفع,  الرجاء المحاولة لاحقاً.";
            } else {

                // Extract the payment url and construct the success message
                $paymentUrl = $paymentData['url'];
                $message = " *لقد اخترت الباقة  $plan->name عدد الاسئلة $plan->question_limit  بسعر $plan->price* " . ($plan->type == SubscriptionPlanType::INTERNATIONAL ? "$" : "دينار ليبي ") . "\n" .                   " اضغط على الرابط أدناه لاكمال عملية الدفع: \n$paymentUrl";
                " اضغط على الرابط أدناه لاكمال عملية الدفع: \n$paymentUrl";
            }

            // Send the payment message to the user via WhatsApp\
            $this->whatsAppService->sendText($to, $message,);
        } catch (\Exception $e) {
            Log::error('handleSubscriptionSelection failed', ['error_message' => $e->getMessage(),]);

            $message = "لايمكن اتمام عملية الدفع,  الرجاء المحاولة لاحقاً.";
            $this->whatsAppService->sendText($to, $message,);
        }
    }

    public function subscribeToPlan(string $to, int $planId): void
    {
        // Find the subscription plan by ID
        $plan = SubscriptionPackage::find($planId);

        // If the plan doesn't exist, notify the user
        if (!$plan) {
            $this->whatsAppService->sendText($to, 'الباقة المحددة غير موجودة.');
            return;
        }

        // Create a new subscription for the student
        $student = Student::where('number', $to)->first();
        if ($student) {
            Subscription::create([
                'student_id' => $student->id,
                'package_id' => $plan->id,
                'total_questions' => $plan->question_limit,
                'used_questions' => 0,
            ]);

            // Notify the user about successful subscription
            $this->whatsAppService->sendText($to, "تم الاشتراك في باقة {$plan->name} بنجاح.");
        } else {
            $this->whatsAppService->sendText($to, 'لم يتم العثور على بيانات المستخدم. تأكد من أنك مسجل لدينا.');
        }
    }

    //TODO: Implement the payment method for T-lync payment gateway
    /**
     * Initiates a payment request to T-lync payment gateway.
     * 
     */
}
