<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Models\SubscriptionPlan;
use App\Enums\SubscriptionPlanType;
use App\Models\Subscription;

class UserSubscriptionSeeder extends Seeder
{
    /*
     * This seeder class is responsible for populating the database with user subscription data
     * from a CSV file. The CSV file is expected to contain user information including email,
     * password, and subscription duration in days.
     *
     * The seeder performs the following steps:
     * 1. Reads user data from a specified CSV file.
     * 2. Fetches available subscription plans from the database, grouping them by the subscription
     *    duration (days).
     * 3. Iterates through each user record and determines the appropriate subscription plan
     *    (either local or international) based on the user's specified duration.
     * 4. Distributes users evenly between local and international plans using a round-robin approach.
     * 5. Inserts user subscription data into the database in batches to optimize memory usage
     *    and performance.
     * 6. Commits the transaction to save all changes to the database.
     */

    private $batchSize = 500; // Set the batch size for inserting records to manage memory usage

    public function run()
    {
        // Define the path to the CSV file containing user data
        $filePath = base_path('database/seeders/data/users.csv');
        if (!File::exists($filePath)) {
            $this->command->error("CSV file not found: $filePath");
            return; // Exit if the CSV file does not exist
        }

        // Open the CSV file for reading
        $handle = fopen($filePath, 'r');

        // Skip the header row of the CSV file
        fgetcsv($handle);

        // Fetch subscription plans from the database and group them by period
        $plans = SubscriptionPlan::select('id', 'period', 'type')->get()->groupBy('period');

        // Begin a database transaction to ensure data integrity
        DB::beginTransaction();

        // Initialize an array to hold user data from the CSV
        $users = [];

        // Read each row of the CSV file and store user data in the $users array
        while (($data = fgetcsv($handle, 1000, ',')) !== false) {
            $users[] = [
                'email' => trim($data[0]), // Assuming email is in the first column
                'password' => trim($data[1]), // Assuming password is in the second column
                'days' => (int) trim($data[2]), // Assuming days is in the third column
            ];
        }

        // Close the file after reading all user data
        fclose($handle);

        // Initialize arrays to hold user subscription data
        $localUsersData = [];
        $internationalUsersData = [];

        // Initialize counters for local and international users
        $localCount = 0;
        $internationalCount = 0;

        // Process each user to determine their subscription type
        foreach ($users as $user) {
            $days = $user['days'];

            // Retrieve the local and international subscription plans based on the user's days
            $localPlan = $plans[$days]->where('type', SubscriptionPlanType::LOCAL)->first();
            $internationalPlan = $plans[$days]->where('type', SubscriptionPlanType::INTERNATIONAL)->first();

            // Alternate between local and international plans
            if ($localPlan && $internationalPlan) {
                // Assign based on the current count to balance the distribution
                if ($localCount <= $internationalCount) {
                    $localUsersData[] = [
                        'email' => $user['email'],
                        'password' => $user['password'],
                        'subscription_plan_id' => $localPlan->id,
                        'status'=>0,
                    ];
                    $localCount++;
                } else {
                    $internationalUsersData[] = [
                        'email' => $user['email'],
                        'password' => $user['password'],
                        'subscription_plan_id' => $internationalPlan->id,
                        'status'=>0,
                    ];
                    $internationalCount++;
                }
            } elseif ($localPlan) {
                // Only local plan available, add to local users data
                $localUsersData[] = [
                    'email' => $user['email'],
                    'password' => $user['password'],
                    'subscription_plan_id' => $localPlan->id,
                    'status'=>0,
                ];
                $localCount++;
            } elseif ($internationalPlan) {
                // Only international plan available, add to international users data
                $internationalUsersData[] = [
                    'email' => $user['email'],
                    'password' => $user['password'],
                    'subscription_plan_id' => $internationalPlan->id,
                    'status'=>0,
                ];
                $internationalCount++;
            }

            // Insert local users in batches to manage memory usage
            if (count($localUsersData) === $this->batchSize) {
                Subscription::insert($localUsersData);
                $localUsersData = []; // Reset for the next batch
            }

            // Insert international users in batches to manage memory usage
            if (count($internationalUsersData) === $this->batchSize) {
                Subscription::insert($internationalUsersData);
                $internationalUsersData = []; // Reset for the next batch
            }
        }

        // Insert any remaining local users that haven't been inserted yet
        if (!empty($localUsersData)) {
            Subscription::insert($localUsersData);
        }
        // Insert any remaining international users that haven't been inserted yet
        if (!empty($internationalUsersData)) {
            Subscription::insert($internationalUsersData);
        }

        // Commit the transaction to save all changes to the database
        DB::commit();

        // Log a message indicating that user insertion is complete
        $this->command->info("User insertion completed.");
    }
}
