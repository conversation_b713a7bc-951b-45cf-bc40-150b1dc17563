<?php

namespace App\Jobs;

use App\Enums\TransactionStatus;
use App\Mail\sendSubscription;
use App\Models\Transaction;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Twilio\Rest\Client;

class SendEmailAndWhatsAppJob implements ShouldQueue
{
    use Queueable;
    protected $email;
    protected $addrusEmail;
    protected $addrusPassword;
    protected $whatsappNumber;
    protected $uuid;

    /**
     * Create a new job instance.
     */
    public function __construct($uuid, $email, $addrusEmail, $addrusPassword, $whatsappNumber)
    {
        $this->uuid = $uuid;
        $this->email = $email;
        $this->addrusEmail = $addrusEmail;
        $this->addrusPassword = $addrusPassword;
        $this->whatsappNumber = $whatsappNumber;
    }


    public function handle(): void
    {
        $emailSent = $this->sendEmail();
        $whatsAppSent = $this->sendWhatsAppMessage();

        // Update the transaction only if both email and WhatsApp were sent successfully
        if ($emailSent && $whatsAppSent) {
            $this->updateTransaction();
        } else {
            Log::error('Email or WhatsApp failed, transaction not updated.', [
                'uuid' => $this->uuid,
                'emailSent' => $emailSent,
                'whatsAppSent' => $whatsAppSent
            ]);
        }
    }

    private function sendEmail()
    {
        try {
            Mail::to($this->email)->send(new sendSubscription($this->addrusEmail, $this->addrusPassword));
            Log::info('Email sent successfully', ['email' => $this->email]);
            return true;
        } catch (\Exception $e) {
            Log::error('Email sending failed', ['error' => $e->getMessage()]);
            return false;
        }
    }
    private function sendWhatsAppMessage()
    {
        try {
            $message = "تم الاشتراك في منصة ادرس لتعليم الالكتروني:\nالبريد الالكتروني: {$this->addrusEmail}\nرمز المرور: {$this->addrusPassword}";
            $sid = env('TWILIO_SID');
            $token = env('TWILIO_AUTH_TOKEN');
            $from = env('TWILIO_WHATSAPP_FROM');
            $to = 'whatsapp:' . $this->whatsappNumber;

            $client = new Client($sid, $token);
            $client->messages->create($to, [
                'from' => $from,
                'body' => $message
            ]);

            Log::info('WhatsApp message sent successfully', ['to' => $this->whatsappNumber]);
            return true;
        } catch (\Exception $e) {
            Log::error('WhatsApp message sending failed', ['error' => $e->getMessage()]);
            return false;
        }
    }

    private function updateTransaction()
    {
        try {
            Transaction::where('uuid', $this->uuid)->update(['status' => TransactionStatus::COMPLETED]);
            Log::info('Transaction updated to completed', ['uuid' => $this->uuid]);
        } catch (\Exception $e) {
            Log::error('Failed to update transaction', ['uuid' => $this->uuid, 'error' => $e->getMessage()]);
        }
    }
}
