<x-layout dir="rtl" title="الإشتراك من داخل ليبيا" subtitle="سيتم أرسال معلومات تسجيل الدخول للتطبيق على بريدك الإلكتروني و رقم الهاتف المرتبط بالواتس اب (WhatsApp)">
    {{-- Subscription Form --}}
    <form class="container mx-auto my-5 p-2" action="/tlync" method="POST" id="subscriptionForm">
        @csrf {{-- Form Field Protection --}}
        {{-- Subscription Form Card --}}
        <div class="card mx-auto">
            <div class="card-body p-0 bg-light bg-gradient">
                {{-- Title --}}
                <section class="text-center my-5">
                    <h1>أختر الباقة</h1>
                </section>
                {{-- Subscription Radio Cards --}}
                <section class="px-5 d-flex justify-content-center flex-wrap">
                    <!-- Subscription Radio Card Loop -->
                    @foreach($plans as $plan)
                    <div class="col">
                        <label class="card p-4 mb-4 mx-auto" style="width: 16rem;">
                            <input type="radio" name="plan_id" class="d-none" value="{{ $plan->id }}" {{$plan->id == old('plan_id') ? 'checked' : ''}}>
                            <div class="card-body text-center">
                                <h2 class="card-title text-primary user-select-none">{{$plan->title}}</h2>
                                <h1 class="card-title py-3 user-select-none">{{$plan->price}} <br /> دينار</h1>
                                <p class="card-text text-secondary fs-4 user-select-none">أشتراك {{$plan->period}} يوم</p>
                            </div>
                        </label>
                    </div>
                    @endforeach
                    <!-- End Subscription Radio Card Loop -->
                </section>
                {{-- Title --}}
                <section class="text-center my-5">
                    <h1>خدمة السحب من الرصيد (المدار أو ليبيانا)</h1>
                </section>
                {{-- TCP Card Section --}}
                <section class="px-5 d-flex justify-content-center flex-wrap">
                    <div class="col">
                        <label class="card p-4 mb-4 mx-auto" style="width: 16rem;">
                            <div class="card-body text-center">
                                <h2 class="card-title text-primary user-select-none">الإشتراك الإسبوعي</h2>
                                <h1 class="card-title py-3 user-select-none">2 <br />دينار</h1>
                                {{-- <p class="card-text text-secondary fs-4"></p> --}}
                                <a href="https://addrus.com/subscribe" target="_blank" class="btn btn-primary bg-white card-text text-secondary fs-4 border-0 user-select-none stretched-link">
                                    أشتراك 7 يوم
                                </a>
                            </div>
                        </label>
                    </div>
                </section>
                {{-- User Details Card --}}
                <section class="d-flex flex-wrap card-group my-5 mx-1 mx-md-5">
                    <div class="card flex-grow-1 mx-auto p-3" style="max-width: 800px;">
                        <div class="card-body p-0">
                            {{-- Title --}}
                            <h1 class="text-center mb-5">أدخل بياناتك للحصول علي الحساب</h1>
                            {{-- Email Section --}}
                            <div class="w-100 w-md-50">
                                {{-- Email Address Input --}}
                                <h5 class="fs-6">البريد الإلكتروني <span class="fs-6 text-secondary">(أختياري)<span></h5>
                                <div class="input-group mb-4" dir="ltr">
                                    <input type="text" class="form-control" id="email" name="email" placeholder="<EMAIL>" value="{{ old('email') }}">
                                    <span class="input-group-text">
                                        <i class="bi bi-email text-dark">@</i>
                                    </span>
                                </div>
                            </div>
                            {{-- Phone Section --}}
                            <div class="w-100 w-md-50">
                                <h5 class="fs-6">رقم هاتف المرتبط بالواتس اب (WhatsApp) <span class="fs-6 text-danger">*<span></h5>
                                {{-- Phone Number Input --}}
                                <div class="input-group mb-4" dir="ltr">
                                    <input type="text" class="form-control" id="phone" name="phone" placeholder="0912345678" value="{{ old('phone') }}">
                                    <span class="input-group-text">
                                        <i class="bi bi-phone text-dark"></i>
                                    </span>
                                </div>
                            </div>
                            {{-- Next Button --}}
                            <div class="text-center d-grid gap-2 w-100 w-md-50 mx-auto">
                                <button type="button" id="otp-button" class="btn btn-lg btn-primary">
                                    <span id="otp-button-text">التالي</span>
                                    <span id="otp-button-spinner" class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                                </button>
                            </div>
                            {{-- Validation Error Messages --}}
                            <div id="detailsValidation" class="w-100 w-md-50 mx-auto mt-3" dir="rtl"></div>
                        </div>
                    </div>
                </section>
                {{-- OTP Card --}}
                <section class="d-flex flex-wrap card-group my-5 mx-1 mx-md-5 d-none" id="otpCard">
                    <div class="card flex-grow-1 mx-auto p-3" style="max-width: 800px;">
                        <div class="card-body p-0">
                            {{-- Title --}}
                            <h1 class="text-center mb-3">أدخل رمز التحقق</h1>
                            <h5 class="text-center mb-5 text-black-50" dir="rtl">تم إرسال رمز التحقق في رسالة عبر الواتس اب (WhatsApp).</h5>
                            {{-- OTP Section --}}
                            <div class="w-100 w-md-50" dir="ltr">
                                {{-- OTP Input --}}
                                <div class="input-group mb-md-4">
                                    <span class="input-group-text position-relative w-25 d-md-block d-none">
                                        {{-- Resend OTP --}}
                                        <button type="button" class="btn position-absolute top-50 start-50 translate-middle" id="resend-otp-button">
                                            <i class="bi text-dark fs-10" id="resend-otp-button-text">
                                                إعادة الإرسال
                                            </i>
                                        </button>
                                    </span>
                                    <input type="text" class="form-control text-center" name="otp" id="otp" dir="ltr">
                                    <span class="input-group-text position-relative px-4 d-md-block d-none">
                                        <i class="bi bi-key text-dark position-absolute top-50 start-50 translate-middle"></i>
                                    </span>
                                </div>
                                <div class="input-group my-4 d-md-none">
                                    <span class="input-group-text justify-content-center w-100">
                                        <button type="button" class="btn" id="resend-otp-button">
                                            <i class="bi text-dark fs-10" id="resend-otp-button-text">
                                                إعادة الإرسال
                                            </i>
                                        </button>
                                    </span>
                                </div>
                            </div>
                            {{-- Submit Button --}}
                            <div class="text-center d-grid gap-2 w-100 w-md-50 mx-auto">
                                <button type="submit" id="submit-button" class="btn btn-lg btn-primary">
                                    <span id="button-text">التالي</span>
                                    <span id="button-spinner" class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                                </button>
                            </div>
                            {{-- Validation Error Messages --}}
                            <div id="otpValidation" class="w-100 w-md-50 mx-auto mt-3" dir="rtl">
                                @if($errors->any())
                                <div class="alert alert-danger">
                                    <ul>
                                        @foreach($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </form> {{-- End Subscription Form --}}
    {{-- Form Scripts --}}
    <script src="{{ asset('assets/js/local_form.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {

            if ("{{old('phone')}}" != "") {
                const otpCard = document.getElementById("otpCard");

                document.getElementById('email').disabled = true;
                document.getElementById('phone').disabled = true;
                document.querySelectorAll('input[name="plan_id"]').forEach(rb => rb.disabled = true);

                otpButton.classList.add('d-none');
                otpCard.classList.remove('d-none');
                otpCard.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });

    </script>
</x-layout>
