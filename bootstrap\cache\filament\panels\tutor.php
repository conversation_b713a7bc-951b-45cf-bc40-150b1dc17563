<?php return array (
  'livewireComponents' => 
  array (
    'app.filament.tutor.pages.answer-comments' => 'App\\Filament\\Tutor\\Pages\\AnswerComments',
    'app.filament.tutor.pages.answer-question' => 'App\\Filament\\Tutor\\Pages\\AnswerQuestion',
    'app.filament.tutor.pages.question-requests' => 'App\\Filament\\Tutor\\Pages\\QuestionRequests',
    'app.filament.tutor.pages.tutor-questions' => 'App\\Filament\\Tutor\\Pages\\TutorQuestions',
    'app.filament.tutor.pages.tutor-settings' => 'App\\Filament\\Tutor\\Pages\\TutorSettings',
    'app.filament.tutor.pages.view-and-accept-question' => 'App\\Filament\\Tutor\\Pages\\ViewAndAcceptQuestion',
    'app.filament.tutor.widgets.grades-breakdown-chart' => 'App\\Filament\\Tutor\\Widgets\\GradesBreakdownChart',
    'app.filament.tutor.widgets.subjects-breakdown-chart' => 'App\\Filament\\Tutor\\Widgets\\SubjectsBreakdownChart',
    'app.filament.tutor.widgets.tutor-performance-stats' => 'App\\Filament\\Tutor\\Widgets\\TutorPerformanceStats',
    'filament.pages.dashboard' => 'Filament\\Pages\\Dashboard',
    'filament.livewire.database-notifications' => 'Filament\\Livewire\\DatabaseNotifications',
    'filament.pages.auth.edit-profile' => 'Filament\\Pages\\Auth\\EditProfile',
    'filament.livewire.global-search' => 'Filament\\Livewire\\GlobalSearch',
    'filament.livewire.notifications' => 'Filament\\Livewire\\Notifications',
    'filament.pages.auth.login' => 'Filament\\Pages\\Auth\\Login',
  ),
  'clusters' => 
  array (
  ),
  'clusteredComponents' => 
  array (
  ),
  'clusterDirectories' => 
  array (
  ),
  'clusterNamespaces' => 
  array (
  ),
  'pages' => 
  array (
    'C:\\xampp\\htdocs\\work\\addrus-pay\\app\\Filament\\Tutor\\Pages\\AnswerComments.php' => 'App\\Filament\\Tutor\\Pages\\AnswerComments',
    'C:\\xampp\\htdocs\\work\\addrus-pay\\app\\Filament\\Tutor\\Pages\\AnswerQuestion.php' => 'App\\Filament\\Tutor\\Pages\\AnswerQuestion',
    'C:\\xampp\\htdocs\\work\\addrus-pay\\app\\Filament\\Tutor\\Pages\\QuestionRequests.php' => 'App\\Filament\\Tutor\\Pages\\QuestionRequests',
    'C:\\xampp\\htdocs\\work\\addrus-pay\\app\\Filament\\Tutor\\Pages\\TutorQuestions.php' => 'App\\Filament\\Tutor\\Pages\\TutorQuestions',
    'C:\\xampp\\htdocs\\work\\addrus-pay\\app\\Filament\\Tutor\\Pages\\TutorSettings.php' => 'App\\Filament\\Tutor\\Pages\\TutorSettings',
    'C:\\xampp\\htdocs\\work\\addrus-pay\\app\\Filament\\Tutor\\Pages\\ViewAndAcceptQuestion.php' => 'App\\Filament\\Tutor\\Pages\\ViewAndAcceptQuestion',
    0 => 'Filament\\Pages\\Dashboard',
    1 => 'App\\Filament\\Tutor\\Pages\\TutorQuestions',
    2 => 'App\\Filament\\Tutor\\Pages\\AnswerQuestion',
    3 => 'App\\Filament\\Tutor\\Pages\\AnswerComments',
    4 => 'App\\Filament\\Tutor\\Pages\\QuestionRequests',
    5 => 'App\\Filament\\Tutor\\Pages\\ViewAndAcceptQuestion',
    6 => 'App\\Filament\\Tutor\\Pages\\TutorSettings',
  ),
  'pageDirectories' => 
  array (
    0 => 'C:\\xampp\\htdocs\\work\\addrus-pay\\app\\Filament/Tutor/Pages',
  ),
  'pageNamespaces' => 
  array (
    0 => 'App\\Filament\\Tutor\\Pages',
  ),
  'resources' => 
  array (
  ),
  'resourceDirectories' => 
  array (
  ),
  'resourceNamespaces' => 
  array (
  ),
  'widgets' => 
  array (
    'C:\\xampp\\htdocs\\work\\addrus-pay\\app\\Filament\\Tutor\\Widgets\\GradesBreakdownChart.php' => 'App\\Filament\\Tutor\\Widgets\\GradesBreakdownChart',
    'C:\\xampp\\htdocs\\work\\addrus-pay\\app\\Filament\\Tutor\\Widgets\\SubjectsBreakdownChart.php' => 'App\\Filament\\Tutor\\Widgets\\SubjectsBreakdownChart',
    'C:\\xampp\\htdocs\\work\\addrus-pay\\app\\Filament\\Tutor\\Widgets\\TutorPerformanceStats.php' => 'App\\Filament\\Tutor\\Widgets\\TutorPerformanceStats',
    0 => 'App\\Filament\\Tutor\\Widgets\\TutorPerformanceStats',
    1 => 'App\\Filament\\Tutor\\Widgets\\SubjectsBreakdownChart',
    2 => 'App\\Filament\\Tutor\\Widgets\\GradesBreakdownChart',
  ),
  'widgetDirectories' => 
  array (
    0 => 'C:\\xampp\\htdocs\\work\\addrus-pay\\app\\Filament/Tutor/Widgets',
  ),
  'widgetNamespaces' => 
  array (
    0 => 'App\\Filament\\Tutor\\Widgets',
  ),
);