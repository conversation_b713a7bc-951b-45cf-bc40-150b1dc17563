models:
  SubscriptionPlan:
    type: enum:LOCAL,INTERNATIONAL
    period: integer
    price: decimal:10,2
    created_at: timestamp
    updated_at: timestamp
    expiration_date: timestamp
    relationships:
      hasMany: Subscription

  Subscription:
    email: string:255
    password: string:100
    status: boolean
    subscription_plan_id: id foreign:subscription_plans
    created_at: timestamp
    updated_at: timestamp
    relationships:
      belongsTo: SubscriptionPlan
      hasMany: Transaction

  Transaction:
    email: string:255
    phone: string:20
    subscription_id: id foreign:subscriptions
    payment_provider: enum:TLYNC,STRIPE
    amount: decimal:10,2
    currency: enum:USD,EUR,LYD
    status: enum:PENDING,COMPLETED,FAILED default:PENDING
    created_at: timestamp
    updated_at: timestamp
    uuid: uuid unique
    relationships:
      belongsTo: Subscription
      hasMany: PaymentCallback

  PaymentCallback:
    transaction_id: id foreign:transactions
    response: json nullable
    callback_time: timestamp
    created_at: timestamp
    updated_at: timestamp
    relationships:
      belongsTo: Transaction

seeders: SubscriptionPlan, Subscription, Transaction, PaymentCallback
