<?php

namespace App\Models\AskMe;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Subject extends Model
{

    protected $fillable = ['name'];
    protected $connection = 'mysql_secondary';

    protected $casts = [ 'id' => 'integer' ];

    public function tutorSubjects(): HasMany { return $this->hasMany(TutorSubject::class); }
    public function session(): Has<PERSON><PERSON> { return $this->hasMany(UserQuestionSession::class); }
    public function questions(): Has<PERSON><PERSON> { return $this->hasMany(Question::class); }
}
