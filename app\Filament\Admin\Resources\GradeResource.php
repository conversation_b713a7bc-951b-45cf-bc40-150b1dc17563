<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\GradeResource\Pages;
use App\Models\AskMe\Grade;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

/**
 * Grade Resource
 *
 * Manages educational grades in the admin panel.
 * Provides CRUD operations for the Grade model.
 */
class GradeResource extends Resource
{
    /**
     * The model that this resource manages
     */
    protected static ?string $model = Grade::class;

    /**
     * The icon displayed in the navigation sidebar
     */
    protected static ?string $navigationIcon = 'heroicon-s-paper-clip';

    /**
     * Get the title for this resource page
     *
     * @return string  title
     */
    public function getTitle(): string
    {
        return __('filament-panels.grade.plural');
    }

    /**
     * Get the singular label for this resource
     *
     * @return string The localized singular label
     */
    public static function getModelLabel(): string
    {
        return __('filament-panels.grade.singular');
    }

    /**
     * Get the plural label for this resource
     *
     * @return string The localized plural label
     */
    public static function getPluralModelLabel(): string
    {
        return __('filament-panels.grade.plural');
    }

    /**
     * Get the navigation label for this resource
     *
     * @return string The localized navigation label
     */
    public static function getNavigationLabel(): string
    {
        return __('filament-panels.grade.title');
    }
    /**
     * Define the form structure for creating and editing grades
     *
     * @param Form $form The Filament form instance
     * @return Form The configured form
     */
    public static function form(Form $form): Form
    {
        return $form->schema([
            // Name field for the grade
            TextInput::make('name')
                ->label(__('filament-panels.question.fields.grade'))
                ->required()
                ->minLength(3)
                ->maxLength(255),
        ]);
    }

    /**
     * Define the table structure for displaying grades
     *
     * @param Table $table The Filament table instance
     * @return Table The configured table
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                // Primary column for grade name
                TextColumn::make('name')
                    ->label(__('filament-panels.question.fields.grade'))
                    ->sortable()
                    ->searchable(),

                // Creation timestamp column
                TextColumn::make('created_at')
                    ->label(__('filament-panels.question.fields.created_at'))
                    ->dateTime(),
            ])
            ->actions([
                // Row-level actions
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                // Actions that can be performed on multiple records
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }


    public static function getRelations(): array
    {
        return [
        ];
    }

    public static function getPages(): array
    {
        return [
            // List page 
            'index' => Pages\ListGrades::route('/'),
            // Create new grade page
            'create' => Pages\CreateGrade::route('/create'),
            // Edit existing grade page
            'edit' => Pages\EditGrade::route('/{record}/edit'),
        ];
    }
}
