<?php

namespace App\Http\Requests\Api;
use App\Rules\validateOtp;

use Illuminate\Foundation\Http\FormRequest;

class InitiatePaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        
        return [
            'plan_id' => 'required|integer|exists:subscription_plans,id',
            'phone' => 'required|string|regex:/^09\d{8}$/',  
            'email' => 'nullable|email', 
        ];
    }

    public function messages(): array
    {
        return [
            'plan_id.required' => 'يجب تحديد باقة قبل الضغط على زر التالي',

            'phone.required' => 'حقل رقم الهاتف مطلوب.',
            'phone.regex' => 'رقم الهاتف يجب أن يتكون من 10 أرقام ويبدأ ب 09.',

            'email.email' => 'البريد الإلكتروني يجب أن يكون عنوان بريد إلكتروني صالح.',

        ];
    }
}
