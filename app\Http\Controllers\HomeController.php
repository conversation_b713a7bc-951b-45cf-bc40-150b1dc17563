<?php

namespace App\Http\Controllers;

use App\Enums\SubscriptionPlanType;
use App\Jobs\CheckUsersAndCreate;
use App\Jobs\ProcessAccountData;
use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Jobs\SendText;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Cache;
class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        // $this->middleware('auth');
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        return view('welcome');
    }
    public function getSubscriptionPlans($type)
    {
        $planType = SubscriptionPlanType::INTERNATIONAL;
        if ($type == 'local') {
            $planType = SubscriptionPlanType::LOCAL;
        }
        $currentDateTime = Carbon::now('Africa/Tripoli')->format('Y-m-d H:i:s');
        // Fetch Subscription Plan where expired day is after the current date and time
        $plans = SubscriptionPlan::where("type", $planType)->where('is_active', 1)->orderBy('price', 'asc')->get();
        if ($type == 'local') {
            return view("local.index", compact('plans'));
        } 
        // else if ($type == 'International') {
        //     return view("international.index", compact('plans'));
        // } 
        else {
            return view("welcome");
        }
    }
    public function createAndSendOtp(Request $request, string $phone)
    {

        $sessionToken = Session::token();
        $requestToken = $request->header('X-CSRF-TOKEN') ?? '';

        if (!hash_equals($sessionToken, $requestToken)) {
            Log::error('CSRF token mismatch on "createAndSendOtp()"', ['sessionToken' => $sessionToken, 'requestToken' => $requestToken]);
            return response()->json(['message' => 'invalid request'], 419);
        }

        try {

            if (preg_match('/^09\d{8}$/', $phone)){
                $validatedPhone = '+218' . substr($phone, 1);
            } elseif(preg_match('/^\+?\d+$/', $phone)){
                $validatedPhone = $phone;
            } else {
                return response()->json(['status' => 'failed', 'message' => 'تأكد من رقم الهاتف وأعد المحاولة'], 400);
            }

            // Check for cooldown using cache with phone number as key
            $cooldownSeconds = 39; // 30 seconds cooldown
            $cacheKey = 'otp_cooldown_' . $validatedPhone;

            if (Cache::has($cacheKey)) {
                $expiresAt = Cache::get($cacheKey);
                $remainingTime = Carbon::now()->diffInSeconds(Carbon::parse($expiresAt));

                return response()->json([
                    'status' => 'cooldown',
                    'message' => "يرجى الانتظار قبل طلب رمز تحقق جديد",
                    'remaining' => $remainingTime
                ], 429);
            }

            // Generate 6-digit OTP
            $otp = str_pad(random_int(100000, 999999), 6, '0', STR_PAD_LEFT);
            $data = [];

            // save the OTP in array to send it to the whatsapp JOB
            $data[] = $otp;

            // Store OTP in session with phone number as part of the key
            // This allows multiple users to request OTPs without conflicts
            session(['otp_' . $validatedPhone => $otp]);

            // Set cooldown for this phone number - store expiration time
            $expiresAt = Carbon::now()->addSeconds($cooldownSeconds);
            Cache::put($cacheKey, $expiresAt, $cooldownSeconds);

            // Send the OTP via whatsapp
            SendText::dispatch($validatedPhone, 'otp', $data);

            return response()->json(['status' => 'success', 'message' => 'OTP sent successfully'], 200);
        } catch (\Exception $error) {
            Log::error('Error with sending OTP', [$error]);
            return response()->json(['status' => 'error', 'message' => 'Failed to send OTP'], 500);
        }
    }

    public function verifyOtp(Request $request, string $phone, string $requestOtp)
    {
        $sessionToken = Session::token();
        $requestToken = $request->header('X-CSRF-TOKEN') ?? '';

        if (!hash_equals($sessionToken, $requestToken)) {
            Log::error('CSRF token mismatch on "verifyOtp()"', ['sessionToken' => $sessionToken, 'requestToken' => $requestToken]);
            return response()->json(['message' => 'invalid request'], 419);
        }

        if (!$phone) {
            return response()->json(['status' => 'failed', 'message' => 'Phone number is required'], 400);
        }

        // Validate and format the phone number
        if (preg_match('/^09\d{8}$/', $phone)){
            $validatedPhone = '+218' . substr($phone, 1);
        } elseif(preg_match('/^\+?\d+$/', $phone)){
            $validatedPhone = $phone;
        } else {
            return response()->json(['status' => 'failed', 'message' => 'Invalid phone number format'], 400);
        }

        // Get the OTP for this specific phone number
        $otp = Session::get('otp_' . $validatedPhone);

        if (!$otp) {
            return response()->json(['status' => 'failed', 'message' => 'No OTP request found for this phone number'], 400);
        }

        if ($otp != $requestOtp) {
            return response()->json(['status' => 'failed', 'message' => 'Invalid OTP'], 400);
        }

        // Clear the OTP after successful verification
        Session::forget('otp_' . $validatedPhone);

        return response()->json(['status' => 'success', 'message' => 'OTP Verified'], 200);
    }

    public function showInstructions()
    {
        return view('instructions');
    }
}
