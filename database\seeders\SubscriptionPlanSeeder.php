<?php

namespace Database\Seeders;

use App\Enums\SubscriptionPlanType;
use App\Models\SubscriptionPlan;
use Illuminate\Database\Seeder;

class SubscriptionPlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        $local_plans = [
            [
                'title' => 'الباقة البرونزية',
                'type' => SubscriptionPlanType::LOCAL->value,
                'period' => 30,
                'price' => 8,
                'expiration_date' => now()->addYear()->endOfYear()
            ],
            [
                'title' => 'الباقة الفضية',
                'type' => SubscriptionPlanType::LOCAL->value,
                'period' => 90,
                'price' => 42,
                'expiration_date' => now()->addYear()->endOfYear()
            ],
            [
                'title' => 'الباقة الذهبية',
                'type' => SubscriptionPlanType::LOCAL->value,
                'period' => 365,
                'price' => 72,
                'expiration_date' => now()->addYear()->endOfYear()
            ]
        ];

        $international_plans = [
            [
                'title' => 'الباقة البرونزية',
                'type' => SubscriptionPlanType::INTERNATIONAL->value,
                'period' => 30,
                'price' => 8,
                'expiration_date' => now()->addYear()->endOfYear()
            ],
            [
                'title' => 'الباقة الفضية',
                'type' => SubscriptionPlanType::INTERNATIONAL->value,
                'period' => 90,
                'price' => 42,
                'expiration_date' => now()->addYear()->endOfYear()
            ],
            [
                'title' => 'الباقة الذهبية',
                'type' => SubscriptionPlanType::INTERNATIONAL->value,
                'period' => 365,
                'price' => 72,
                'expiration_date' => now()->addYear()->endOfYear()
            ]
        ];

        SubscriptionPlan::insert($local_plans);
        SubscriptionPlan::insert($international_plans);
    }
}
