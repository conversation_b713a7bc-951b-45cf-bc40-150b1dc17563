<?php

namespace App\Providers\Filament;

use App\Filament\AddrusPay\Widgets\GeneralStatisticsWidget;
use App\Filament\AddrusPay\Widgets\MonthlyRevenueWidget;
use App\Filament\AddrusPay\Widgets\SubscriptionStatusWidget;
use App\Filament\AddrusPay\Widgets\SubscriptionTypesChartWidget;
use App\Filament\AddrusPay\Widgets\TransactionStatusesChartWidget;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;

class AddrusPayPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->id('addrusPay')
            ->path('addrus-pay-admin') // Your panel URL path
            ->authGuard('addrus_pay_admin') // Custom guard
            ->colors([
                'primary' => Color::Amber,
            ])
            ->spa()
            ->unsavedChangesAlerts()
            // ->databaseNotifications()
            ->sidebarCollapsibleOnDesktop()
            ->discoverResources(in: app_path('Filament/AddrusPay/Resources'), for: 'App\\Filament\\AddrusPay\\Resources')
            ->discoverPages(in: app_path('Filament/AddrusPay/Pages'), for: 'App\\Filament\\AddrusPay\\Pages')
            ->pages([
                Pages\Dashboard::class,
            ])
            ->login()
            // ->user(\App\Models\AddrusPayAdmin::class)
            ->discoverWidgets(in: app_path('Filament/AddrusPay/Widgets'), for: 'App\\Filament\\AddrusPay\\Widgets')
            ->widgets([
                // Widgets\AccountWidget::class,
                // Widgets\FilamentInfoWidget::class,
                GeneralStatisticsWidget::class,
                MonthlyRevenueWidget::class,
                SubscriptionStatusWidget::class,
                // SubscriptionTypesChartWidget::class,
                TransactionStatusesChartWidget::class

            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
                'auth:addrus_pay_admin'
            ]);
    }
}
