<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\SubscriptionPlan;

class SubscriptionPlanFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = SubscriptionPlan::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'type' => fake()->randomElement(["LOCAL","INTERNATIONAL"]),
            'period' => fake()->numberBetween(-10000, 10000),
            'price' => fake()->randomFloat(2, 0, 99999999.99),
            'created_at' => fake()->dateTime(),
            'updated_at' => fake()->dateTime(),
            'expiration_date' => fake()->dateTime(),
        ];
    }
}
