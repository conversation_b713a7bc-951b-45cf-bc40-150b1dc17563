{{--
    Reports Page View
    This view allows administrators to generate reports with different time ranges.
    The page includes form controls for selecting report type and time range,
    and displays the generated PDF report.
--}}
<x-filament::page>
    <div class="grid gap-4 max-w-xl">
        <h1 class="text-2xl font-bold">توليد تقرير </h1>

        {{-- Report Type Dropdown - Select the type of report to generate --}}
        <div>
            {{-- Label for report type selection --}}
            <label class="block mb-1 text-sm">نوع التقرير</label>

            {{-- Dropdown for selecting report type --}}
            <select wire:model.defer="reportType" class="w-full border rounded p-2">
                <option value="tutors">تقرير المعلمين</option>
                <option value="revenue">تقرير الإيرادات</option>
                <option value="subscriptions">تقرير الاشتراكات</option>
            </select>
        </div>

        {{-- Time Range Dropdown - Select the time period for the report --}}
        <div>
            {{-- Label for time range selection --}}
            <label class="block mb-1 text-sm">الفترة الزمنية</label>

            {{-- Dropdown that triggers the updateRange method when changed --}}
            <select wire:change="updateRange($event.target.value)" class="w-full border rounded p-2">
                <option value="week">الأسبوع</option>
                <option value="month">الشهر</option>
                <option value="year">السنة</option>
            </select>
        </div>


        {{-- Month Selection Dropdown - Only shown when month is selected as the time range --}}
        @if ($range === 'month')
        <div>
            <label class="block mb-1 text-sm">اختر الشهر</label>

            <select wire:model.defer="selectedMonth" class="w-full border rounded p-2">
                @foreach ($availableMonths as $number => $month)
                <option value="{{ $number }}">{{ $month }}</option>
                @endforeach
            </select>
        </div>
        @endif

        {{-- Generate Report Button  --}}
        <div>
            <x-filament::button wire:click="generateReport" color="primary">
                توليد التقرير
            </x-filament::button>
        </div>
    </div>

    {{-- PDF Preview Section - Only shown after a report has been generated --}}
    @if ($pdfUrl)
    <div class="mt-10">
        <h2 class="text-lg font-semibold mb-2">معاينة التقرير</h2>

        <iframe src="{{ $pdfUrl }}" width="100%" height="600" class="rounded border shadow-md"></iframe>

        <div class="mt-4">
            <a href="{{ $pdfUrl }}" target="_blank"
                class="inline-flex items-center px-4 py-2 bg-danger-600 text-white rounded hover:bg-danger-700">
                تحميل PDF
            </a>
        </div>
    </div>
    @endif
</x-filament::page>