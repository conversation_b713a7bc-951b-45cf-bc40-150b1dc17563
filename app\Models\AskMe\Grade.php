<?php

namespace App\Models\AskMe;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Grade extends Model
{

    protected $fillable = ['name'];
    protected $connection = 'mysql_secondary';
    protected $casts = [ 'id' => 'integer' ];
    public function session(): HasMany { return $this->hasMany(UserQuestionSession::class); }
    public function tutorSubjects(): HasM<PERSON> { return $this->hasMany(TutorSubject::class); }
    public function questions(): HasM<PERSON> { return $this->hasMany(Question::class); }
}
