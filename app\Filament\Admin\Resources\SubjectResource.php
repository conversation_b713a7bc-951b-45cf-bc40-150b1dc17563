<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\SubjectResource\Pages;
use App\Models\AskMe\Subject;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

/**
 * Subject Resource
 *
 * Manages subjects in the admin panel.
 * Provides functionality to create, view, edit, and delete subject records.
 */
class SubjectResource extends Resource
{
    /**
     * The model that this resource manages
     */
    protected static ?string $model = Subject::class;
    protected static ?string $navigationIcon = 'heroicon-o-document';

    /**
     * Get the title for this resource page
     *
     * @return string The localized title
     */
    public function getTitle(): string
    {
        return __('filament-panels.subject.plural');
    }

    /**
     * Get the singular label for this resource
     *
     * @return string The localized singular label
     */
    public static function getModelLabel(): string
    {
        return __('filament-panels.subject.singular');
    }

    /**
     * Get the plural label for this resource
     *
     * @return string The localized plural label
     */
    public static function getPluralModelLabel(): string
    {
        return __('filament-panels.subject.plural');
    }

    /**
     * Get the navigation label for this resource
     *
     * @return string The localized navigation label
     */
    public static function getNavigationLabel(): string
    {
        return __('filament-panels.subject.title');
    }
    /**
     * Define the form structure for creating and editing subjects
     *
     * @param Form $form The Filament form instance
     * @return Form The configured form
     */
    public static function form(Form $form): Form
    {
        return $form->schema([
            // Subject name field
            TextInput::make('name')
                ->label(__('filament-panels.question.fields.subject'))
                ->required()
                ->minLength(3)
                ->maxLength(255),
        ]);
    }

    /**
     * Define the table structure for displaying subjects
     *
     * @param Table $table The Filament table instance
     * @return Table The configured table
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                // Subject name column
                TextColumn::make('name')
                    ->label(__('filament-panels.question.fields.subject'))
                    ->sortable()
                    ->searchable(),

                // Creation date column
                TextColumn::make('created_at')
                    ->label(__('filament-panels.question.fields.created_at'))
                    ->dateTime(),
            ])
            ->actions([
                // Row-level actions
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                // Actions that can be performed on multiple records
                Tables\Actions\DeleteBulkAction::make(),
            ]);
    }


    public static function getRelations(): array
    {
        return [
        ];
    }

    /**
     * Define the pages for the Subject resource
     *
     * @return array The pages configuration
     */
    public static function getPages(): array
    {
        return [
            // List page (main index view)
            'index' => Pages\ListSubjects::route('/'),
            // Create new subject page
            'create' => Pages\CreateSubject::route('/create'),
            // Edit existing subject page
            'edit' => Pages\EditSubject::route('/{record}/edit'),
        ];
    }
}
