<!DOCTYPE html>
<html lang="ar">

<head>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: 'DejaVu Sans', sans-serif;
            direction: rtl;
            text-align: right;
            font-size: 14px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th,
        td {
            border: 1px solid #ccc;
            padding: 8px;
            text-align: center;
        }

        th {
            background-color: #eee;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
        }

        .divider {
            border: none;
            border-top: 2px solid #333;
            margin: 20px 0;
        }
    </style>
</head>

<body>
    <div class="header">
        <h1>أدرس للتعليم الإلكتروني</h1>
        <h3>خدمة اسألني</h3>

        <p style="font-size: 18px; font-weight: bold; margin-top: 10px;">
            تقرير المعلمين
        </p>

        
        <p style="margin-top: 5px;">
            <?php if($range == 'month' && $month): ?>
            لشهر <?php echo e($month); ?>

            <?php elseif($range == 'year'): ?>
            للسنة الحالية: <?php echo e(now()->format('Y')); ?>

            <?php elseif($range == 'week'): ?>
            للأسبوع من <?php echo e(now()->startOfWeek()->format('d-m-Y')); ?> إلى <?php echo e(now()->endOfWeek()->format('d-m-Y')); ?>

            <?php else: ?>
            للفترة المحددة
            <?php endif; ?>
        </p>

        
        <p style="margin-top: 10px; font-size: 14px; color: #666;">
            يحتوي هذا التقرير على ترتيب المعلمين بناءً على عدد الأسئلة المجابة ومتوسط وقت الإجابة خلال الفترة الزمنية المختارة.
        </p>
    </div>



    <hr class="divider">

    <table>
        <thead>
            <tr>
                <th>الترتيب</th>
                <th>المعلم</th>
                <th>الأسئلة المجابة</th>
                <th>متوسط وقت الإجابة</th>
            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $tutors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $i => $tutor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <tr>
                <td><?php echo e($i + 1); ?></td>
                <td><?php echo e($tutor->user->name ?? '—'); ?></td>
                <td><?php echo e($tutor->answered_count); ?></td>
                <td><?php echo e($tutor->avg_answer_time ? $tutor->avg_answer_time . ' دقيقة' : '—'); ?></td>
            </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>
</body>

</html><?php /**PATH C:\xampp\htdocs\work\addrus-pay\resources\views/exports/tutors.blade.php ENDPATH**/ ?>