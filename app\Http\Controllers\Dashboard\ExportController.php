<?php

namespace App\Http\Controllers\Dashboard;

use App\Enums\TransactionStatus;
use App\Enums\TutorActivities;
use App\Http\Controllers\Controller;
use App\Models\AskMe\Subscription;
use App\Models\AskMe\Transaction;
use App\Models\AskMe\Tutor;
use App\Models\AskMe\TutorActivity;
use Illuminate\Support\Facades\DB;
use Mpdf\Mpdf;

/**
 * ExportController
 *
 * This controller handles the generation of PDF reports for the dashboard.
 */
class ExportController extends Controller
{

    /**
     *
     * This method routes the request to report generator
     * based on the requested report type.
     *
     * @param string $type The type of report to generate ('tutors', 'revenue', or 'subscriptions')
     * @param string $range The time range for the report ('week', 'month', or 'year')
     * @param string|null $month The specific month number (1-12) when range is 'month'
     * @return \Illuminate\Http\Response PDF response containing the generated report
     */
    public function generate(string $type, string $range, ?string $month = null)
    {
        switch ($type) {
            case 'tutors':
                return $this->generateTutorsReport($range, $month);

            case 'revenue':
                return $this->generateRevenueReport($range, $month);

            case 'subscriptions':
                return $this->generateSubscriptionsReport($range, $month);

            default:
                abort(404);
        }
    }

    /**
     * Generate a PDF report of tutor performance
     *
     * This method creates a report showing tutor performance metrics including:
     * - Number of questions answered within the specified time range
     * - Average response time for each tutor
     *
     * @param string $range The time range for the report ('week', 'month', or 'year')
     * @param string|null $month The specific month number (1-12) when range is 'month'
     * @return  PDF response containing the tutor report
     */
    private function generateTutorsReport(string $range, ?string $month = null)
    {
        // Build query to get tutors with their answered question count
        $query = Tutor::with('user')
            ->withCount(['questions as answered_count' => function ($q) use ($range, $month) {
                // Only count questions that have been answered
                $q->whereNotNull('answer_text');

                // Apply date filters based on the selected range
                if ($range === 'month' && $month) {
                    // Filter by specific month if provided
                    $q->whereMonth('questions.created_at', $month);
                }
                if ($range === 'year') {
                    // Filter by current year
                    $q->whereYear('questions.created_at', now()->year);
                }
                if ($range === 'week') {
                    // Filter by current week (Sunday to Saturday)
                    $q->whereBetween('questions.created_at', [
                        now()->startOfWeek(),  // Start of current week
                        now()->endOfWeek()     // End of current week
                    ]);
                }
            }]);

        // Get tutors and calculate average response time for each
        $tutors = $query->get()
            ->map(function ($tutor) use ($range, $month) {
                // Build query to calculate average response time
                $activityQuery = TutorActivity::query()
                    ->where('action', TutorActivities::Answer)  
                    ->where('tutor_activities.tutor_id', $tutor->id) 
                    ->join('questions', 'tutor_activities.question_id', '=', 'questions.id'); 

                // Apply the same date filters to ensure consistency with question count
                if ($range === 'month' && $month) {
                    $activityQuery->whereMonth('questions.created_at', $month);
                }
                if ($range === 'year') {
                    $activityQuery->whereYear('questions.created_at', now()->year);
                }
                if ($range === 'week') {
                    $activityQuery->whereBetween('questions.created_at', [
                        now()->startOfWeek(),
                        now()->endOfWeek()
                    ]);
                }

                // Calculate average response time in seconds, then convert to minutes
                $avgSeconds = $activityQuery->avg(DB::raw('TIMESTAMPDIFF(SECOND, questions.created_at, tutor_activities.created_at)'));
                $tutor->avg_answer_time = $avgSeconds ? round($avgSeconds / 60, 1) : null;

                return $tutor;
            });

        // Render the HTML for the PDF using the tutors view
        $html = view('exports.tutors', compact('tutors', 'range', 'month'))->render();

        // Configure the PDF generator
        $mpdf = new \Mpdf\Mpdf([
            'mode' => 'utf-8',          
            'format' => 'A4',          
            'default_font' => 'sans',   
            'margin_left' => 10,       
            'margin_right' => 10,
            'margin_top' => 10,
            'margin_bottom' => 10,
        ]);

        // Generate the PDF from the HTML
        $mpdf->WriteHTML($html);

        return response($mpdf->Output('tutor-report.pdf', 'I'), 200)
            ->header('Content-Type', 'application/pdf');
    }

    /**
     * Generate a PDF report of revenue statistics
     *
     * This method creates a report showing revenue metrics including:
     * - Total revenue for the specified time period
     * - Number of completed transactions
     * - Daily breakdown of transactions and revenue
     *
     * @param string $range The time range for the report ('week', 'month', or 'year')
     * @param string|null $month The specific month number (1-12) when range is 'month'
     * @return PDF response containing the revenue report
     */
    private function generateRevenueReport(string $range, ?string $month = null)
    {
        $query = Transaction::query()
            ->where('status', TransactionStatus::COMPLETED);

        // Apply date filters based on the selected range
        if ($range === 'month' && $month) {
            // Filter by specific month if provided
            $query->whereMonth('created_at', $month);
        }
        if ($range === 'year') {
            // Filter by current year
            $query->whereYear('created_at', now()->year);
        }
        if ($range === 'week') {
            // Filter by current week (Sunday to Saturday)
            $query->whereBetween('created_at', [
                now()->startOfWeek(),  // Start of current week
                now()->endOfWeek()     // End of current week
            ]);
        }

        // Calculate summary statistics
        $totalRevenue = $query->sum('amount');
        $transactionsCount = $query->count();

        // Get daily breakdown of transactions and revenue
        $dailyRevenues = $query->selectRaw('DATE(created_at) as date, COUNT(*) as transactions_count, SUM(amount) as total_amount')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Prepare data for the view and render the HTML
        $html = view('exports.revenue', [
            'totalRevenue' => $totalRevenue,
            'transactionsCount' => $transactionsCount,
            'dailyRevenues' => $dailyRevenues,
            'range' => $range,
            'month' => $month,
            'weekStart' => now()->startOfWeek()->format('d/m/Y'),
            'weekEnd' => now()->endOfWeek()->format('d/m/Y'),
        ])->render();

        // Configure the PDF generator
        $mpdf = new \Mpdf\Mpdf([
            'mode' => 'utf-8',         
            'format' => 'A4',           
            'default_font' => 'sans',   
            'margin_left' => 10,     
            'margin_right' => 10,
            'margin_top' => 10,
            'margin_bottom' => 10,
        ]);

        // Generate the PDF from the HTML
        $mpdf->WriteHTML($html);

        // Return the PDF as a downloadable response
        return response($mpdf->Output('revenue-report.pdf', 'I'), 200)
            ->header('Content-Type', 'application/pdf');
    }

    /**
     * Generate a PDF report of subscription statistics
     *
     * This method creates a report showing subscription metrics including:
     * - Total number of subscriptions for the specified time period
     * - Daily breakdown of new subscriptions
     * - Distribution of subscriptions by package type
     *
     * @param string $range The time range for the report ('week', 'month', or 'year')
     * @param string|null $month The specific month number (1-12) when range is 'month'
     * @return PDF response containing the subscriptions report
     */
    private function generateSubscriptionsReport(string $range, ?string $month = null)
    {
        $query = Subscription::query();

        // Apply date filters based on the selected range
        if ($range === 'month' && $month) {
            // Filter by specific month if provided
            $query->whereMonth('created_at', $month);
        }
        if ($range === 'year') {
            // Filter by current year
            $query->whereYear('created_at', now()->year);
        }
        if ($range === 'week') {
            // Filter by current week (Sunday to Saturday)
            $query->whereBetween('created_at', [
                now()->startOfWeek(),  // Start of current week
                now()->endOfWeek()     // End of current week
            ]);
        }

        // Calculate total number of subscriptions
        $totalSubscriptions = $query->count();

        // Get daily breakdown of new subscriptions
        $dailySubscriptions = $query->selectRaw('DATE(created_at) as date, COUNT(*) as subscriptions_count')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $packageSubscriptions = $query->selectRaw('subscription_packages_id, COUNT(*) as subscriptions_count')
            ->groupBy('subscription_packages_id')
            ->with('package')  
            ->get();

        // Prepare data for the view and render the HTML
        $html = view('exports.subscriptions', [
            'totalSubscriptions' => $totalSubscriptions,
            'dailySubscriptions' => $dailySubscriptions,
            'packageSubscriptions' => $packageSubscriptions,
            'range' => $range,
            'month' => $month,
            'weekStart' => now()->startOfWeek()->format('d/m/Y'),
            'weekEnd' => now()->endOfWeek()->format('d/m/Y'),
        ])->render();

        // Configure the PDF generator
        $mpdf = new \Mpdf\Mpdf([
            'mode' => 'utf-8',         
            'format' => 'A4',          
            'default_font' => 'sans',  
            'margin_left' => 10,       
            'margin_right' => 10,
            'margin_top' => 10,
            'margin_bottom' => 10,
        ]);

        // Generate the PDF from the HTML
        $mpdf->WriteHTML($html);

        // Return the PDF as a downloadable response
        return response($mpdf->Output('subscriptions-report.pdf', 'I'), 200)
            ->header('Content-Type', 'application/pdf');
    }
}
