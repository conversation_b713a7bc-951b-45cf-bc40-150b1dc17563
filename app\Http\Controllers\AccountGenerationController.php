<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class AccountGenerationController extends Controller
{
/**
 * Generates a specified number of student accounts, assigns them to courses, 
 * and creates the necessary user and student records.
 **
 */
    public function ganerateAccout(Request $request)
    {
        $request->validate([
            'number' => 'required|numeric',
            'number_days' => 'required',
        ],);
        for ($i = 1; $i <= $request->number; $i++) {
            $codefound = 0;
            do {
                $codefound = 0;
                $code = strtolower($this->generateRandomString(6));
                //Check if the generated email already exists in the database
                if (User::where(['email' => $code . '@addrus.com'])->exists())
                    $codefound = 1;
            } while ($codefound == 1);
            // Generate another random string for password
            $code1 = strtolower($this->generateRandomNumber(6));
            $email = $code . '@addrus.com';
            try {
                $user = User::create([
                    'name' => $code,
                    'slug' => $code,
                    'email' => $email,
                    'password' => Hash::make($code1),
                    'user_type' => "Student",
                    'number_days' => $request->number_days,
                    'auto' => 1,
                    'verified' => 1,
                ]);
                // Create a student record linked to the user
                Student::create([
                    'name' => $code,
                    'slug' => $code,
                    'email' => $email,
                    'user_id' => $user->id,
                    'first_plain_password' => $code1,
                ]);

                // Get all published courses and enroll the new user in each course
                $courses = Course::Published()->pluck('id');
                foreach ($courses as $course) {
                    Enrollment::create([
                        'user_id' => $user->id,
                        'course_id' => $course,
                    ]);
                }
            } catch (\Exception $e) {
                Log::error("Error creating account: " . $e->getMessage());

                return response()->json(['message' => 'Error creating account' . $e]);
            }
        }
        Log::info("Accounts created successfully.");
        return response()->json(['message' => 'Accounts ganerated successfully', 'Accounts' => $insertedUsers,]);
    }
    
    function generateRandomString($length = 5)
    {
        $characters = '**********abcdefghilkmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[random_int(0, $charactersLength - 1)];
        }
        return $randomString;
    }
    function generateRandomNumber($length = 5)
    {
        $characters = '**********';
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[random_int(0, $charactersLength - 1)];
        }
        return $randomString;
    }

}
