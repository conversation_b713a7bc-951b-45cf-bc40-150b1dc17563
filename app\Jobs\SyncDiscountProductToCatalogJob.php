<?php

namespace App\Jobs;

use App\Models\Discount;
use App\Models\Products;
use App\Services\CatalogSyncService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Log;
/**
 * SyncDiscountProductToCatalogJob
 *
 * This job is responsible for syncing or removing a product's discount to/from the Meta Catalog.
 * 
 * It works in two modes:
 * - sync Sends the discounted product to the catalog.
 * - remove Updates the catalog to remove the discount .
 *
 * After a successful API response, it updates the pivot table discount_products to reflect 
 * whether the discount is added or removed (via is_added_catelog)
 */

class SyncDiscountProductToCatalogJob implements ShouldQueue
{
    use Queueable;

    protected Products $product;
    protected Discount $discount;
    protected string $mode = 'sync';

    public function __construct($product, $discount, $mode)
    {
        $this->product = $product;
        $this->discount = $discount;
        $this->mode = $mode;
    }


    public function handle(): void
    {
        if ($this->mode === 'remove') {
            $payload['sale_price'] = 0;
            $response = app(CatalogSyncService::class)->updateDiscountProduct($this->product, $payload);
        } else {
            $response = app(CatalogSyncService::class)->updateDiscountProduct($this->product);
        }
        $json = $response->json();
        if (!isset($json['success']) || $json['success'] != true) {
            Log::error('Catalog sync failed', $json);
            return;
        }
        $this->product->discounts()->updateExistingPivot($this->discount->id, [
            'is_added_catelog' => ($this->mode === 'remove') ? 0 : 1,
        ]);
    }
}
