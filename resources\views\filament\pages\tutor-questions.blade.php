<x-filament::page>
    <div x-data="{ tab: 'all' }">
        {{-- Tabs Header --}}
        <div class="flex gap-4 border-b mb-4">
            <button x-on:click="tab = 'all'"
                class="py-2 px-4 border-b-2"
                :class="tab === 'all' 
                ? 'border-blue-500 text-blue-600 font-semibold' 
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'">
                {{__('filament-panels.question.tabs.all')}} ({{ $allQuestions->count() }})
            </button>

            <button x-on:click="tab = 'answered'"
                class="py-2 px-4 border-b-2"
                :class="tab === 'answered' 
                ? 'border-blue-500 text-blue-600 font-semibold' 
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'">
                {{__('filament-panels.question.tabs.answered')}} ({{ $answeredQuestions->count() }})
            </button>

            <button x-on:click="tab = 'unanswered'"
                class="py-2 px-4 border-b-2"
                :class="tab === 'unanswered' 
                ? 'border-blue-500 text-blue-600 font-semibold' 
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'">
                {{__('filament-panels.question.tabs.unanswered')}} ({{ $unansweredQuestions->count() }})
            </button>
        </div>

        {{-- All Questions --}}
        <div x-show="tab === 'all'" class="space-y-4">
            @foreach ($allQuestions as $question)
            @include('filament.partials.question-card', ['question' => $question])
            @endforeach
        </div>

        {{-- Answered Questions --}}
        <div x-show="tab === 'answered'" class="space-y-4" x-cloak>
            @forelse ($answeredQuestions as $question)
            @include('filament.partials.question-card', ['question' => $question])
            @empty
            <p class="text-gray-500"> {{__('filament-panels.question.body.no_question')}}</p>
            @endforelse
        </div>

        {{-- Unanswered Questions --}}
        <div x-show="tab === 'unanswered'" class="space-y-4" x-cloak>
            @forelse ($unansweredQuestions as $question)
            @include('filament.partials.question-card', ['question' => $question])
            @empty
            <p class="text-gray-500">{{__('filament-panels.question.body.all_answered')}} 🎉</p>
            @endforelse
        </div>
    </div>
</x-filament::page>