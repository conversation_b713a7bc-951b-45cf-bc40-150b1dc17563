<?php

namespace App\Jobs;

use App\Enums\Currency;
use App\Enums\PaymentProvider;
use App\Enums\TransactionStatus;
use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use App\Models\Transaction;
use App\Services\WhatsAppService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\DB;
use App\Jobs\CheckTransactionStatus;

class WhatsAppTlync implements ShouldQueue
{
    use Queueable;
    public $tries = 1;              
    public $maxExceptions = 1;    
    public $timeout = 60;          

    protected string $phone;
    protected int $planId;

    public function __construct(string $phone, int $planId)
    {
        $this->phone = $phone;
        $this->planId = $planId;
    }
    /**
     * Initiates a payment request to T-lync payment gateway.
     * 
     * 1- Validates the incoming request.
     * 2- Extracts the amount, phone number, and subscription plan ID.
     * 3- Generates a unique reference for the transaction.
     * 4- Validates the uniqueness of that reference.
     * 5- Prepares the API payload.
     * 6- Calls the T-lync payment gateway API.
     * 7- Handles the API response.
     * 8- Updates the subscription status and creates a new transaction record.
     * 10- Dispatches a job to check the transaction status.
     */

    public function handle()
    {
        try {
            $customRef = Str::uuid()->toString();

            $validator = Validator::make(
                [
                    'custom_ref' => $customRef,
                    'phone' => $this->phone,
                    'plan_id' => $this->planId,
                ],
                [
                    'custom_ref' => 'required|string|unique:transactions,uuid',
                    'phone' => 'required|regex:/^\\+2189\\d{8}$/',
                    'plan_id' => 'required|integer|exists:subscription_plans,id',
                ],
            );

            if ($validator->fails()) {
                throw new ValidationException($validator);
            }

            $amount = SubscriptionPlan::where('id', $this->planId)->value('price');

            $data = [
                'id' => config('api.store_id'),
                'amount' => $amount,
                'phone' => $this->phone,
                'backend_url' => config('api.backend_url'),
                'custom_ref' => $customRef,
                'frontend_url' => config('api.whatsapp_frontend_url') . '/' . $customRef,
                'failed_front_end_url' => config('api.whatsapp_failed_frontend_url') . '/' . $customRef,
            ];

            Log::info('Tlync Payment Payload', [$data]);

            $response = Http::timeout(60)
                ->withHeaders(['Accept' => 'application/json'])
                ->withToken(config('api.api_token'))
                ->post(config('api.payment_api_live'), $data);
            if (!$response->successful()) {
                Log::error('Tlync API returned error: ' . $response->body());
                $this->notifyPaymentFailure();
                return;
            }
            $responseData = $response->json();

            DB::transaction(function () use ($data) {
                $subscription = Subscription::where('subscription_plan_id', $this->planId)
                    ->where('status', 0)
                    ->select('id', 'email', 'password')
                    ->first();

                if (!$subscription) {
                    throw new \Exception('No available subscription found');
                }

                $subscription->status = 1;
                $subscription->save();

                // CheckUsersAndCreate::dispatch();

                $transaction = Transaction::create([
                    'email' => '',
                    'phone' => $data['phone'],
                    'subscription_id' => $subscription->id,
                    'payment_provider' => PaymentProvider::TLYNC,
                    'amount' => $data['amount'],
                    'currency' => Currency::LYD,
                    'status' => TransactionStatus::PENDING,
                    'uuid' => $data['custom_ref'],
                ]);

                $transaction->load('subscription');
                CheckTransactionStatus::dispatch($transaction)->delay(now()->addMinutes(1));
            });

            // Send WhatsApp message with payment URL
            $plan = SubscriptionPlan::find($this->planId);
            $price = number_format($plan->price, 2);
            $currency = 'دينار ليبي';
            $paymentUrl = $responseData['url'];

            $message = " *لقد اخترت الباقة  {$plan->title} مدة الاشتراك {$plan->period} يوما بسعر {$price} {$currency}*\n";
            $message .= "اضغط على الرابط أدناه لاكمال عملية الدفع:\n{$paymentUrl}";

            try {
                (new WhatsAppService())->sendText($this->phone, $message, 'AddrusPay');
            } catch (\Exception $e) {
                Log::error('Failed to send WhatsApp message: ' . $e->getMessage());
            }
        } catch (\Exception $error) {
            Log::error(' Error: ' . $error->getMessage());
            $this->notifyPaymentFailure();
        }
    }

    private function notifyPaymentFailure(): void
    {
        try {
            $msg =  "لايمكن اتمام عملية الدفع,  الرجاء المحاولة لاحقاً.";
            (new WhatsAppService())->sendText($this->phone, $msg, 'AddrusPay');
        } catch (\Exception $e) {
            Log::error('Failed to send WhatsApp failure message: ' . $e->getMessage());
        }
    }
}
