const interfaceTranslations = {
  "selectedCountryAriaLabel": "Valt land",
  "noCountrySelected": "Inget land valt",
  "countryListAriaLabel": "Lista över länder",
  "searchPlaceholder": "<PERSON>ök",
  "zeroSearchResults": "Inga resultat hittades",
  "oneSearchResult": "1 resultat hittades",
  "multipleSearchResults": "${count} resultat hittades",
  // additional countries (not supported by country-list library)
  "ac": "Ascension",
  "xk": "Kosovo"
};
export default interfaceTranslations;
