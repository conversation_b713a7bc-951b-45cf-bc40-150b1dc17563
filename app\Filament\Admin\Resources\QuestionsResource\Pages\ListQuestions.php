<?php


namespace App\Filament\Admin\Resources\QuestionsResource\Pages;

use App\Filament\Admin\Resources\QuestionsResource;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;

/**
 * List Questions Page
 *
 * This page displays a list of questions with tabs for filtering by answer status.
 */
class ListQuestions extends ListRecords
{
    /**
     * The resource class that this page displays
     */
    protected static string $resource = QuestionsResource::class;

    /**
     * Define the tabs for filtering questions
     *
     * Creates three tabs:
     * - Unanswered: Shows questions with no answer text or URL
     * - Answered: Shows questions with either answer text or URL
     * - All: Shows all questions without filtering
     *
     * @return array The tabs configuration
     */
    public function getTabs(): array
    {
        return [
            // Tab for questions that haven't been answered yet
            'unanswered' => Tab::make(__("filament-panels.question.tabs.unanswered"))
                ->modifyQueryUsing(
                    // Filter for records where both answer fields are null
                    fn($query) => $query->whereNull('answer_text')
                        ->whereNull('answer_url')
                ),

            // Tab for questions that have been answered
            'answered' => Tab::make(__("filament-panels.question.tabs.answered"))
                ->modifyQueryUsing(
                    // Filter for records where at least one answer field is not null
                    fn($query) => $query->where(function ($q) {
                        $q->whereNotNull('answer_text')
                            ->orWhereNotNull('answer_url');
                    })
                ),

            // Tab for all questions without filtering
            'all' => Tab::make(__("filament-panels.question.tabs.all")),
        ];
    }


    protected function getHeaderActions(): array
    {
        return [
        ];
    }
}
