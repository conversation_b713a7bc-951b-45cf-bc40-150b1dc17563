<?php

namespace App\Console\Commands;

use App\Jobs\SyncDiscountProductToCatalogJob;
use App\Models\Discount;
use App\Models\DiscountProduct;
use App\Models\Products;
use App\Services\CatalogSyncService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

/**
 * This command syncs active discounts to the catalog
 * and removes expired ones that  previously added.
 */

class SyncActiveDiscounts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-active-discounts';

    /**
     * The console command description.
     *
     * @var string
     */

    protected $description = 'Sync active discounts to the catalog and remove expired ones if needed.';
    /**
     * Execute the console command.
     *
     * For each product:
     * - If it has an active discount that hasn't been added to the catalog, dispatch a sync job.
     * - Otherwise, check the latest discount that was added to the catalog.
     *   - If it's expired and hasn't been removed yet, dispatch a remove job.
     */
    public function handle()
    {
        Log::info(' Starting SyncActiveDiscounts command');
    
        $products = Products::with(['discounts' => function ($query) {
            $query->withPivot('is_added_catelog');
        }])->get();
    
        Log::info('Total products found: ' . $products->count());
    
        foreach ($products as $product) {
            Log::info("Processing product ID: {$product->id}");
    
            $discount = $product->getActiveDiscount();
    
            if ($discount) {
                Log::info("Found active discount ID: {$discount->id} for product ID: {$product->id}");
    
                if ($discount->pivot && $discount->pivot->is_added_catelog === 0) {
                    Log::info("Dispatching SYNC job for product ID {$product->id}, discount ID {$discount->id}");
                    SyncDiscountProductToCatalogJob::dispatch($product, $discount, 'sync');
                    continue;
                }
    
            } else {
                Log::info("No active discount for product ID: {$product->id}");
            }
    
            // Now check if the last synced discount is expired
            $pivot = DiscountProduct::where('product_id', $product->id)
                ->where('is_added_catelog', true)
                ->latest('id')
                ->first();
    
            if (! $pivot) {
                Log::info("No synced discount found in pivot for product ID: {$product->id}");
                continue;
            }
    
            $discount = $product->discounts->firstWhere('id', $pivot->discount_id)
                ?? Discount::find($pivot->discount_id);
    
            if (! $discount) {
                Log::warning("Discount not found for pivot ID: {$pivot->id}");
                continue;
            }
    
            if ($discount->end_at && $discount->end_at < now()) {
                Log::info("Dispatching REMOVE job for expired discount ID {$discount->id}, product ID {$product->id}");
                SyncDiscountProductToCatalogJob::dispatch($product, $discount, 'remove');
            } else {
                Log::info("Discount still valid for product ID {$product->id}");
            }
        }
    
        Log::info('Finished SyncActiveDiscounts command');
    }
    
}
