<?php
namespace App\Console\Commands;

use App\Enums\TransactionStatus;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

use App\Models\Transaction;
use App\Models\Subscription;
use Carbon\Carbon;

class UpdateTransactionStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-transaction-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update older pending transactions and associated subscriptions.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Update all pending transactions that are older than 2 days
        $twoDaysAgo = Carbon::now()->subDay(2);

        try {
            DB::transaction(function () use ($twoDaysAgo) {

                // Get all subscription IDs for old pending transactions
                $subscriptionIds = Transaction::query()
                    ->where('status', TransactionStatus::PENDING)
                    ->where('created_at', '<=', $twoDaysAgo)
                    ->pluck('subscription_id')
                    ->unique();

                // Update all subscriptions to inactive
                if ($subscriptionIds->isNotEmpty()) {
                    Subscription::whereIn('id', $subscriptionIds)
                        ->update(['status' => 0]);
                }

                // Update all old pending transactions to cancelled
                Transaction::query()
                    ->where('status', TransactionStatus::PENDING)
                    ->where('created_at', '<=', $twoDaysAgo)
                    ->update(['status' => TransactionStatus::CANCELED]);
            });

            Log::info('Subscriptions and transactions updated successfully.');
            
        } catch (\Exception $error) {
            
            Log::error('Failed to update subscriptions and transactions: ' . $error->getMessage());
        }
    }
}
