<?php
namespace App\Filament\Admin\Pages;

use App\Enums\WhatsAppMessageType;
use App\Models\AskMe\User;
use App\Models\AskMe\WhatsAppMessages;
use Closure;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\TextInput;
use Illuminate\Support\Facades\Auth;
use Outerweb\FilamentSettings\Filament\Pages\Settings as BaseSettings;
use Outerweb\Settings\Models\Setting;


class Settings extends BaseSettings
{
    protected static ?int $navigationSort = 3;

    /**
     * Initialize the settings form when the page is loaded
     *
     * This method:
     * - Calls the parent mount method to set up the base form
     * - Loads settings from both the Settings table and WhatsAppMessages table
     * - Fills the form with the current values
     */
    public function mount(): void
    {
        // Initialize the base settings form
        parent::mount();

        // Load settings from the database and fill the form
        $this->form->fill([
            // General settings from the Settings table
            'subscription_required' => Setting::get('subscription_required'),
            'question_limit' => Setting::get('question_limit'),

            // WhatsApp message templates from the WhatsAppMessages table
            WhatsAppMessageType::WELCOME->value =>
                WhatsAppMessages::where('type', WhatsAppMessageType::WELCOME)->value('message'),
            WhatsAppMessageType::WELCOME_TRIAL->value =>
                WhatsAppMessages::where('type', WhatsAppMessageType::WELCOME_TRIAL)->value('message'),
            WhatsAppMessageType::WELCOME_SUBSCRIPTION->value =>
                WhatsAppMessages::where('type', WhatsAppMessageType::WELCOME_SUBSCRIPTION)->value('message'),
            WhatsAppMessageType::WELCOME_TUTOR->value =>
                WhatsAppMessages::where('type', WhatsAppMessageType::WELCOME_TUTOR)->value('message'),
        ]);
    }
    /**
     * Define the form schema for the settings page
     *
     * @return array|Closure The form schema definition
     */
    public function schema(): array|Closure
    {
        return [
            // Create a tabbed interface for organizing settings
            Tabs::make('Settings')
                ->schema([
                    // WhatsApp settings tab 
                    WhatsAppSettingsTab::getTab(),

                    // General settings tab
                    Tabs\Tab::make(__('filament-panels.setting.general'))
                        ->schema([
                            // Admin password field with encryption
                            TextInput::make('admin.password')
                                ->label(__('filament-panels.setting.password'))
                                ->minLength(4)
                                ->maxLength(255)
                                // Encrypt the password when saving
                                ->dehydrateStateUsing(fn($state) => $state ? bcrypt($state) : null)
                                // Clear the password field after loading
                                ->afterStateHydrated(fn($component) => $component->state(null))
                                ->password(),

                            // Password confirmation field
                            TextInput::make('password_confirm')
                                ->label(__('filament-panels.setting.password_confirm'))
                                ->password()
                                ->dehydrated(false)
                                // Must match the admin.password field
                                ->same('admin.password')
                        ]),
                ])
        ];
    }
    /**
     *
     * This method is called automatically after the form is submitted and validated.
     * It handles:
     * - Saving WhatsApp message templates to the WhatsAppMessages table
     * - Updating the admin user's password if it was changed
     * - Cleaning up settings from the Settings table that are now stored elsewhere
     */
    protected function afterSave(): void
    {
        // Get all form data
        $data = $this->form->getState();

        // Process WhatsApp message templates
        foreach (
            [
                WhatsAppMessageType::WELCOME->value,
                WhatsAppMessageType::WELCOME_TRIAL->value,
                WhatsAppMessageType::WELCOME_SUBSCRIPTION->value,
                WhatsAppMessageType::WELCOME_TUTOR->value,
            ] as $key
        ) {
            // Save each message to the WhatsAppMessages table
            WhatsAppMessages::updateOrCreate(
                ['type' => $key], 
                ['message' => $data[$key] ?? ''] 
            );

            // Remove from the Settings table since it's now in WhatsAppMessages
            Setting::where('key', $key)->delete();
        }

        // Handle admin password update
        $user = Auth::user();
        $password = Setting::get('admin.password');

        if ($password) {
            // Update the user's password in the User table
            User::where('id', $user->id)->update([
                'password' => $password,
            ]);

            Setting::where('key', 'admin.password')->delete();
        }
    }

    /**
     * Get the singular label for this model
     *    
     * @return string The translated singular label
     */
    public static function getModelLabel(): string
    {
        return __('filament-panels.setting.singular');
    }

    /**
     * Get the plural label for this model
     *     *
     * @return string The translated plural label
     */
    public static function getPluralModelLabel(): string
    {
        return __('filament-panels.setting.plural');
    }

    /**
     * Get the navigation label for this page
     *
     *
     * @return string The translated navigation label
     */
    public static function getNavigationLabel(): string
    {
        return __('filament-panels.setting.title');
    }

    /**
     * Get the title for this page
     *
     *
     * @return string The translated page title
     */
    public function getTitle(): string
    {
        return __('الإعدادات العامة'); 
    }
}
