<?php
namespace App\Filament\Admin\Widgets;

use App\Enums\TransactionStatus;
use App\Models\AskMe\Subscription;
use App\Models\AskMe\SubscriptionPackage;
use App\Models\AskMe\Transaction;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

/**
 * Admin Finance Stats Widget
 *
 * Displays financial statistics and metrics on the admin dashboard.
 */
class AdminFinanceStats extends BaseWidget
{
    protected static ?int $sort = 1;

    /**
     * Get the financial statistics to display in the widget
     *
     * Calculates and returns various financial metrics including:
     * - Total revenue
     * - Monthly revenue
     * - Today's revenue
     * - Total subscriptions
     * - Active subscribers
     * - Most popular subscription package
     *
     * @return array The stats configuration
     */
    protected function getStats(): array
    {
        // Calculate total revenue from completed transactions
        $totalRevenue = Transaction::where('status', TransactionStatus::COMPLETED)->sum('amount');

        // Calculate revenue for the current month
        $monthlyRevenue = Transaction::where('status', TransactionStatus::COMPLETED)
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->sum('amount');

        // Count total subscriptions
        $totalSubscriptions = Subscription::count();

        // Count active subscribers (those with remaining questions)
        $activeSubscribers = Subscription::whereColumn('total_questions', '>', 'used_questions')->count();

        // Find the most popular subscription package
        $popularPackage = SubscriptionPackage::withCount('subscriptions')
            ->orderByDesc('subscriptions_count')
            ->first();

        // Calculate today's revenue
        $todayRevenue = Transaction::where('status', 'PAID')
            ->whereDate('created_at', now())
            ->sum('amount');

        // Calculate daily revenue for the current month (for chart)
        $startOfMonth = now()->startOfMonth();
        $endOfToday = now()->endOfDay();

        // Collect daily revenue data for the chart
        $daysInMonth = collect();
        for ($date = $startOfMonth->copy(); $date->lte($endOfToday); $date->addDay()) {
            $dailyRevenue = Transaction::where('status', TransactionStatus::COMPLETED)
                ->whereDate('created_at', $date)
                ->sum('amount');

            $daysInMonth->push(round($dailyRevenue, 2));
        }

        // Return array of stat cards to display in the widget
        return [
            // Total revenue stat card
            Stat::make(__('filament-panels.dashboard.total_revenue'), number_format($totalRevenue, 2) . ' د.ل')
                ->icon('heroicon-o-banknotes')
                ->color('success'),

            // Monthly revenue stat card with chart
            Stat::make(__('filament-panels.dashboard.monthly_revenue'), number_format($monthlyRevenue, 2) . ' د.ل')
                ->icon('heroicon-o-calendar')
                ->color('info')
                ->chart($daysInMonth->toArray()), // Display daily revenue chart

            // Today's revenue stat card
            Stat::make(__('filament-panels.dashboard.today_revenue'), number_format($todayRevenue, 2) . ' د.ل')
                ->description(__('filament-panels.dashboard.today_revenue_desc'))
                ->icon('heroicon-o-currency-dollar')
                ->color('primary'),

            // Total subscriptions stat card
            Stat::make(__('filament-panels.dashboard.total_subscriptions'), $totalSubscriptions)
                ->icon('heroicon-o-archive-box')
                ->color('primary'),

            // Active subscribers stat card
            Stat::make(__('filament-panels.dashboard.active_subscribers'), $activeSubscribers)
                ->description(__('filament-panels.dashboard.active_subscribers_desc'))
                ->icon('heroicon-o-user-group')
                ->color('success'),

            // Most popular package stat card
            Stat::make(__('filament-panels.dashboard.top_package'), $popularPackage?->name ?? '—')
                ->description(__('filament-panels.dashboard.top_package_desc'))
                ->icon('heroicon-o-star')
                ->color('warning'),
        ];
    }

    /**
     * Calculate trend data for the past 7 days
     *
     * This helper method generates an array of values for the past 7 days
     * by applying the provided callback function to each day.
     *
     * @param callable $queryCallback Function to execute for each day
     * @return array Array of trend values for the past 7 days
     */
    protected function getTrend(callable $queryCallback): array
    {
        $trend = [];

        // Loop through the past 7 days (6 days ago to today)
        for ($i = 6; $i >= 0; $i--) {
            // Get the date for this iteration
            $date = now()->subDays($i)->startOfDay();

            // Execute the callback for this date and add the result to the trend array
            $trend[] = $queryCallback($date);
        }

        return $trend;
    }
}
