<?php

namespace App\Models\AskMe;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Question extends Model
{
    protected $connection = 'mysql_secondary';

    protected $fillable = [
        'student_id',
        'tutor_id',
        'subject_id',
        'grade_id',
        'question_text',
        'answer_text',
        'image_url',
        'image_id',
        'answer_id',
        'answer_url',
        'answer_type'
    ];

    protected $casts = [
        'id' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
    public function session(): HasMany
    {
        return $this->hasMany(TutorQuestionSessions::class);
    }
    public function comment_session(): HasMany
    {
        return $this->hasMany(CommentSessions::class);
    }

    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }
    public function tutor(): BelongsTo
    {
        return $this->belongsTo(Tutor::class);
    }
    public function subject(): BelongsTo
    {
        return $this->belongsTo(Subject::class);
    }
    public function grade(): BelongsTo
    {
        return $this->belongsTo(Grade::class);
    }
    public function comments(): HasMany
    {
        return $this->hasMany(StudentComment::class);
    }
    public function tutorActivites(): HasMany
    {
        return $this->hasMany(TutorActivity::class);
    }
}
