<?php

namespace App\Filament\AddrusPay\Widgets;

use App\Enums\SubscriptionPlanType;
use App\Models\Transaction;
use App\Models\Subscription;
use App\Enums\TransactionStatus;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class GeneralStatisticsWidget extends BaseWidget
{
    protected static ?int $sort = 1;

    protected ?string $heading = 'الإحصائيات العامة';

    protected function getStats(): array
    {
        return [
            Stat::make('الإيرادات', function() {
                // Calculate total revenue from completed transactions
                return number_format(Transaction::whereHas('subscription.subscriptionPlan', function($query) {
                        $query->where('type', SubscriptionPlanType::LOCAL);
                    })
                    ->where('status', TransactionStatus::COMPLETED->value)
                    ->sum('amount'), 2) . ' د.ل';
            })
                ->description('إجمالي الإيرادات (اشتراكات محلية)')
                ->descriptionIcon('heroicon-o-currency-dollar')
                ->color('success'),

            Stat::make('عدد المعاملات', Transaction::count())
                ->description('إجمالي المعاملات')
                ->descriptionIcon('heroicon-o-banknotes')
                ->color('success'),

            Stat::make('الاشتراكات المستخدمة', Subscription::where('status', 1)->count())
                ->description('الاشتراكات المستخدمة')
                ->descriptionIcon('heroicon-o-check-badge')
                ->color('info'),

            Stat::make('الاشتراكات غير المستخدمة', Subscription::where('status', 0)->count())
                ->description('الاشتراكات غير المستخدمة بعد')
                ->descriptionIcon('heroicon-o-clock')
                ->color('warning'),

        ];
    }
}