<?php

namespace App\Filament\Admin\Widgets;

use App\Enums\TutorActivities;
use App\Models\AskMe\Tutor;
use App\Models\AskMe\TutorActivity;
use Filament\Tables\Columns\TextColumn;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

/**
 * TopTutors Widget
 *
 * Displays a table of top tutors ranked by the number of questions they have answered.
 */
class TopTutors extends BaseWidget
{

    protected static ?int $sort = 8;
    protected int | string | array $columnSpan = 'full';
    protected static ?string $heading = 'أفضل المعلمين (حسب عدد الإجابات)';

    /**
     * Get the localized heading for the widget
     *
     * @return string The localized widget heading
     */
    protected function getHeading(): string
    {
        return __('filament-panels.dashboard.top_tutors');
    }

    /**
     * Build the query for the tutors table
     *
     * Retrieves tutors with their answer count and orders them by most answers first
     *
     * @return Builder Query builder instance
     */
    protected function getTableQuery(): Builder
    {
        return Tutor::query()
            ->withCount(['questions as answered_count' => function ($query) {
                $query->whereNotNull('answer_text');
            }])
            ->with('user')
            ->orderByDesc('answered_count');
        // ->limit(10);
    }

    /**
     * Define the table columns and their configuration
     *
     * @return array Array of column definitions
     */
    protected function getTableColumns(): array
    {
        return [
            // Tutor name column
            TextColumn::make('user.name')
                ->label(__('filament-panels.dashboard.tutor_name'))
                ->searchable()
                ->sortable(),

            // Number of questions answered column
            TextColumn::make('answered_count')
                ->label(__('filament-panels.dashboard.questions_answered'))
                ->sortable()
                ->badge()
                ->color('success'),

            // Average answer time column (in minutes)
            TextColumn::make('avg_answer_time')
                ->label(__('filament-panels.dashboard.avg_answer_time'))
                ->state(function ($record) {
                    // Calculate average response time in seconds
                    $avgSeconds = TutorActivity::query()
                        ->where('tutor_activities.action', TutorActivities::Answer)
                        ->where('tutor_activities.tutor_id', $record->id)
                        ->join('questions', 'tutor_activities.question_id', '=', 'questions.id')
                        ->avg(DB::raw('TIMESTAMPDIFF(SECOND, questions.created_at, tutor_activities.created_at)'));

                    // Convert seconds to minutes and round to 1 decimal place
                    return $avgSeconds ? round($avgSeconds / 60, 1) : null;
                })
                ->formatStateUsing(fn($state) => $state ? $state . ' دقيقة' : '—')
                ->badge()
                ->color('gray'),
        ];
    }
}
