@props(['title', 'subtitle'])

<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    {{-- BootStrap CSS --}}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.1/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-+0n0xVW2eSR5OomGNYDnhzAbDsOXxcvSN1TPprVMTNDbiYZCxYbOOl7+AMvyTG2x" crossorigin="anonymous" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.3.0/font/bootstrap-icons.css" />
    {{-- Custom CSS --}}
    <link rel="stylesheet" href="{{ asset('assets/styles/app.css') }}" />
    <title>Addrus | Subscriptions</title>
</head>
<body>
    {{-- Header --}}
    <x-header></x-header>
    {{-- Main Content --}}
    <main dir="{{ $attributes->get('dir') }}" class="bg-white">
        <meta name="csrf-token" content="{{ csrf_token() }}">
        {{-- Title Section --}}
        <x-title_section>
            <h3 class="section__title mt-5 fs-1">{{ $title }}</h3>
            <p class="section-heading mb-5 mt-4 fs-5">{{ $subtitle }}</p>
        </x-title_section>
        {{-- Main Content --}}
        {{ $slot }}
        {{-- Stats Section --}}
        <x-stats_section></x-stats_section>
    </main>
    {{-- Footer --}}
    <x-footer></x-footer>
    {{-- BootStrap Scripts --}}
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.1/dist/js/bootstrap.bundle.min.js" integrity="sha384-gtEjrD/SeCtmISkJkNUaaKMoLD0//ElJ19smozuHV6z3Iehds+3Ulb9Bn9Plx0x4" crossorigin="anonymous"></script>
</body>
</html>
