<?php

namespace App\Jobs;

use App\Models\JobProcesses;
use App\Models\Subscription;
use Illuminate\Bus\Batchable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ProcessAccountData implements ShouldQueue
{
    use Batchable, Dispatchable, Queueable;
    use Queueable;

    protected $params; // Parameters to send in API request

    /**
     * Create a new job instance.
     */
    public function __construct($params)
    {
        $this->params = $params;
    }

    /**
     * Execute the job.
     */
    public function handle()
    {

        try {
            $days = $this->params['number_days'];
            $number = $this->params['number'];
            $plan_id = $this->params['plan'];
            $jobProcess = $this->params['job_process'];

            // Define the API url
            $url = config('account.api_generate_url');
            Log::info('url',[$url]);
            $response = Http::timeout(5000) -> withHeaders([
                'Authorization' => 'Bearer ' . config('account.account_api_key'),
                'Accept' => 'application/json',
            ])->post($url, ['number_days' => $days, 'number' => $number]);

            // Check if the request was successful
            if ($response->successful()) {
                $data = $response->json();
                $dataToInsert = [];

                // Check if the Accounts list is not empty 
                if (isset($data['Accounts']) && is_array($data['Accounts'])) {
                    foreach ($data['Accounts'] as $account) {
                        //Inserte account into our database
                        $dataToInsert[] = [
                            'email' => $account['email'],
                            'password' => $account['password'],
                            'subscription_plan_id' => $plan_id,
                            'status' => 0
                        ];
                    }
                    Subscription::insert($dataToInsert);


                    Log::info("Created {$number} new users.");
                } else {
                    Log::error('API returned an invalid response: ' . json_encode($data));
                }
            } else {

                Log::error('API request failed: ' . $response->body());
            }
            if ($jobProcess === 1) {
                $this->incrementCompletedChunks();
            }
        } catch (\Exception $e) {

            Log::error('Job failed: ' . $e->getMessage());
            if ($jobProcess === 1) {
                $this->incrementCompletedChunks();
            }
        }
    }
    protected function incrementCompletedChunks()
    {
        $jobProcess = JobProcesses::latest()->first();

        if ($jobProcess) {
            // Increment the completed chunks counter
            $jobProcess->increment('completed_chunks');
        }
    }
}
