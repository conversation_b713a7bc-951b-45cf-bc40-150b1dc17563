const interfaceTranslations = {
  selectedCountryAriaLabel: "Țara selectată",
  noCountrySelected: "<PERSON>cio țară selectată",
  countryListAriaLabel: "Lista țărilor",
  searchPlaceholder: "<PERSON><PERSON><PERSON><PERSON>",
  zeroSearchResults: "Nici un rezultat gasit",
  oneSearchResult: "1 rezultat găsit",
  multipleSearchResults: "${count} rezultate găsite",
  // additional countries (not supported by country-list library)
  ac: "Insula Ascensiunii",
  xk: "Kosovo"
};
export default interfaceTranslations;
