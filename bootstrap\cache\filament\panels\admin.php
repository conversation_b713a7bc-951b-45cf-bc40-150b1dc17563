<?php return array (
  'livewireComponents' => 
  array (
    'app.filament.admin.pages.settings' => 'App\\Filament\\Admin\\Pages\\Settings',
    'app.filament.admin.resources.grade-resource.pages.create-grade' => 'App\\Filament\\Admin\\Resources\\GradeResource\\Pages\\CreateGrade',
    'app.filament.admin.resources.grade-resource.pages.edit-grade' => 'App\\Filament\\Admin\\Resources\\GradeResource\\Pages\\EditGrade',
    'app.filament.admin.resources.grade-resource.pages.list-grades' => 'App\\Filament\\Admin\\Resources\\GradeResource\\Pages\\ListGrades',
    'app.filament.admin.resources.questions-resource.pages.create-questions' => 'App\\Filament\\Admin\\Resources\\QuestionsResource\\Pages\\CreateQuestions',
    'app.filament.admin.resources.questions-resource.pages.edit-questions' => 'App\\Filament\\Admin\\Resources\\QuestionsResource\\Pages\\EditQuestions',
    'app.filament.admin.resources.questions-resource.pages.list-questions' => 'App\\Filament\\Admin\\Resources\\QuestionsResource\\Pages\\ListQuestions',
    'app.filament.admin.resources.subject-resource.pages.create-subject' => 'App\\Filament\\Admin\\Resources\\SubjectResource\\Pages\\CreateSubject',
    'app.filament.admin.resources.subject-resource.pages.edit-subject' => 'App\\Filament\\Admin\\Resources\\SubjectResource\\Pages\\EditSubject',
    'app.filament.admin.resources.subject-resource.pages.list-subjects' => 'App\\Filament\\Admin\\Resources\\SubjectResource\\Pages\\ListSubjects',
    'app.filament.admin.resources.subscription-package-resource.pages.create-subscription-package' => 'App\\Filament\\Admin\\Resources\\SubscriptionPackageResource\\Pages\\CreateSubscriptionPackage',
    'app.filament.admin.resources.subscription-package-resource.pages.edit-subscription-package' => 'App\\Filament\\Admin\\Resources\\SubscriptionPackageResource\\Pages\\EditSubscriptionPackage',
    'app.filament.admin.resources.subscription-package-resource.pages.list-subscription-packages' => 'App\\Filament\\Admin\\Resources\\SubscriptionPackageResource\\Pages\\ListSubscriptionPackages',
    'app.filament.admin.resources.tutor-activity-resource.pages.create-tutor-activity' => 'App\\Filament\\Admin\\Resources\\TutorActivityResource\\Pages\\CreateTutorActivity',
    'app.filament.admin.resources.tutor-activity-resource.pages.edit-tutor-activity' => 'App\\Filament\\Admin\\Resources\\TutorActivityResource\\Pages\\EditTutorActivity',
    'app.filament.admin.resources.tutor-activity-resource.pages.list-tutor-activities' => 'App\\Filament\\Admin\\Resources\\TutorActivityResource\\Pages\\ListTutorActivities',
    'app.filament.admin.resources.tutor-resource.pages.create-tutor' => 'App\\Filament\\Admin\\Resources\\TutorResource\\Pages\\CreateTutor',
    'app.filament.admin.resources.tutor-resource.pages.edit-tutor' => 'App\\Filament\\Admin\\Resources\\TutorResource\\Pages\\EditTutor',
    'app.filament.admin.resources.tutor-resource.pages.list-tutors' => 'App\\Filament\\Admin\\Resources\\TutorResource\\Pages\\ListTutors',
    'app.filament.admin.pages.reports' => 'App\\Filament\\Admin\\Pages\\Reports',
    'filament.pages.dashboard' => 'Filament\\Pages\\Dashboard',
    'app.filament.admin.widgets.admin-finance-stats' => 'App\\Filament\\Admin\\Widgets\\AdminFinanceStats',
    'app.filament.admin.widgets.admin-general-stats' => 'App\\Filament\\Admin\\Widgets\\AdminGeneralStats',
    'app.filament.admin.widgets.package-performance-table' => 'App\\Filament\\Admin\\Widgets\\PackagePerformanceTable',
    'app.filament.admin.widgets.questions-chart' => 'App\\Filament\\Admin\\Widgets\\QuestionsChart',
    'app.filament.admin.widgets.revenue-chart' => 'App\\Filament\\Admin\\Widgets\\RevenueChart',
    'app.filament.admin.widgets.revenue-comparison-chart' => 'App\\Filament\\Admin\\Widgets\\RevenueComparisonChart',
    'app.filament.admin.widgets.subscription-package-pie-chart' => 'App\\Filament\\Admin\\Widgets\\SubscriptionPackagePieChart',
    'app.filament.admin.widgets.top-tutors' => 'App\\Filament\\Admin\\Widgets\\TopTutors',
    'app.filament.admin.widgets.transaction-status-pie-chart' => 'App\\Filament\\Admin\\Widgets\\TransactionStatusPieChart',
    'filament.livewire.database-notifications' => 'Filament\\Livewire\\DatabaseNotifications',
    'filament.pages.auth.edit-profile' => 'Filament\\Pages\\Auth\\EditProfile',
    'filament.livewire.global-search' => 'Filament\\Livewire\\GlobalSearch',
    'filament.livewire.notifications' => 'Filament\\Livewire\\Notifications',
    'filament.pages.auth.login' => 'Filament\\Pages\\Auth\\Login',
  ),
  'clusters' => 
  array (
  ),
  'clusteredComponents' => 
  array (
  ),
  'clusterDirectories' => 
  array (
  ),
  'clusterNamespaces' => 
  array (
  ),
  'pages' => 
  array (
    0 => 'App\\Filament\\Admin\\Pages\\Settings',
    'C:\\xampp\\htdocs\\work\\addrus-pay\\app\\Filament\\Admin\\Pages\\Reports.php' => 'App\\Filament\\Admin\\Pages\\Reports',
    'C:\\xampp\\htdocs\\work\\addrus-pay\\app\\Filament\\Admin\\Pages\\Settings.php' => 'App\\Filament\\Admin\\Pages\\Settings',
    1 => 'Filament\\Pages\\Dashboard',
    2 => 'App\\Filament\\Admin\\Pages\\Reports',
  ),
  'pageDirectories' => 
  array (
    0 => 'C:\\xampp\\htdocs\\work\\addrus-pay\\app\\Filament/Admin/Pages',
  ),
  'pageNamespaces' => 
  array (
    0 => 'App\\Filament\\Admin\\Pages',
  ),
  'resources' => 
  array (
    'C:\\xampp\\htdocs\\work\\addrus-pay\\app\\Filament\\Admin\\Resources\\GradeResource.php' => 'App\\Filament\\Admin\\Resources\\GradeResource',
    'C:\\xampp\\htdocs\\work\\addrus-pay\\app\\Filament\\Admin\\Resources\\QuestionsResource.php' => 'App\\Filament\\Admin\\Resources\\QuestionsResource',
    'C:\\xampp\\htdocs\\work\\addrus-pay\\app\\Filament\\Admin\\Resources\\SubjectResource.php' => 'App\\Filament\\Admin\\Resources\\SubjectResource',
    'C:\\xampp\\htdocs\\work\\addrus-pay\\app\\Filament\\Admin\\Resources\\SubscriptionPackageResource.php' => 'App\\Filament\\Admin\\Resources\\SubscriptionPackageResource',
    'C:\\xampp\\htdocs\\work\\addrus-pay\\app\\Filament\\Admin\\Resources\\TutorActivityResource.php' => 'App\\Filament\\Admin\\Resources\\TutorActivityResource',
    'C:\\xampp\\htdocs\\work\\addrus-pay\\app\\Filament\\Admin\\Resources\\TutorResource.php' => 'App\\Filament\\Admin\\Resources\\TutorResource',
  ),
  'resourceDirectories' => 
  array (
    0 => 'C:\\xampp\\htdocs\\work\\addrus-pay\\app\\Filament/Admin/Resources',
  ),
  'resourceNamespaces' => 
  array (
    0 => 'App\\Filament\\Admin\\Resources',
  ),
  'widgets' => 
  array (
    'C:\\xampp\\htdocs\\work\\addrus-pay\\app\\Filament\\Admin\\Widgets\\AdminFinanceStats.php' => 'App\\Filament\\Admin\\Widgets\\AdminFinanceStats',
    'C:\\xampp\\htdocs\\work\\addrus-pay\\app\\Filament\\Admin\\Widgets\\AdminGeneralStats.php' => 'App\\Filament\\Admin\\Widgets\\AdminGeneralStats',
    'C:\\xampp\\htdocs\\work\\addrus-pay\\app\\Filament\\Admin\\Widgets\\PackagePerformanceTable.php' => 'App\\Filament\\Admin\\Widgets\\PackagePerformanceTable',
    'C:\\xampp\\htdocs\\work\\addrus-pay\\app\\Filament\\Admin\\Widgets\\QuestionsChart.php' => 'App\\Filament\\Admin\\Widgets\\QuestionsChart',
    'C:\\xampp\\htdocs\\work\\addrus-pay\\app\\Filament\\Admin\\Widgets\\RevenueChart.php' => 'App\\Filament\\Admin\\Widgets\\RevenueChart',
    'C:\\xampp\\htdocs\\work\\addrus-pay\\app\\Filament\\Admin\\Widgets\\RevenueComparisonChart.php' => 'App\\Filament\\Admin\\Widgets\\RevenueComparisonChart',
    'C:\\xampp\\htdocs\\work\\addrus-pay\\app\\Filament\\Admin\\Widgets\\SubscriptionPackagePieChart.php' => 'App\\Filament\\Admin\\Widgets\\SubscriptionPackagePieChart',
    'C:\\xampp\\htdocs\\work\\addrus-pay\\app\\Filament\\Admin\\Widgets\\TopTutors.php' => 'App\\Filament\\Admin\\Widgets\\TopTutors',
    'C:\\xampp\\htdocs\\work\\addrus-pay\\app\\Filament\\Admin\\Widgets\\TransactionStatusPieChart.php' => 'App\\Filament\\Admin\\Widgets\\TransactionStatusPieChart',
    0 => 'App\\Filament\\Admin\\Widgets\\AdminGeneralStats',
    1 => 'App\\Filament\\Admin\\Widgets\\QuestionsChart',
    2 => 'App\\Filament\\Admin\\Widgets\\TopTutors',
    3 => 'App\\Filament\\Admin\\Widgets\\AdminFinanceStats',
    4 => 'App\\Filament\\Admin\\Widgets\\RevenueComparisonChart',
    5 => 'App\\Filament\\Admin\\Widgets\\SubscriptionPackagePieChart',
    6 => 'App\\Filament\\Admin\\Widgets\\TransactionStatusPieChart',
    7 => 'App\\Filament\\Admin\\Widgets\\RevenueChart',
    8 => 'App\\Filament\\Admin\\Widgets\\PackagePerformanceTable',
  ),
  'widgetDirectories' => 
  array (
    0 => 'C:\\xampp\\htdocs\\work\\addrus-pay\\app\\Filament/Admin/Widgets',
  ),
  'widgetNamespaces' => 
  array (
    0 => 'App\\Filament\\Admin\\Widgets',
  ),
);