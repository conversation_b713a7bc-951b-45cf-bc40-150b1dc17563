<?php

namespace App\Filament\Tutor\Pages;

use App\Models\AskMe\Question;
use App\Models\AskMe\Tutor;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Auth;

/**
 * TutorQuestions Page
 *
 * This page displays questions assigned to the current tutor, categorized by
 * their answer status (all, answered, and unanswered).
 */
class TutorQuestions extends Page
{

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document';
    protected static string $view = 'filament.pages.tutor-questions';
    protected static ?string $title = '';
    protected static bool $shouldRegisterNavigation = true;

    /**
     * Get the title for the page
     *
     * @return string The localized page title
     */
    public function getTitle(): string
    {
        return __('filament-panels.question.plural');
    }

    /**
     * Get the singular model label for the page
     *
     * @return string The localized singular model label
     */
    public static function getModelLabel(): string
    {
        return __('filament-panels.question.singular');
    }

    /**
     * Get the plural model label for the page
     *
     * @return string The localized plural model label
     */
    public static function getPluralModelLabel(): string
    {
        return __('filament-panels.question.plural');
    }

    /**
     * Get the navigation label for the sidebar
     *
     * @return string The localized navigation label
     */
    public static function getNavigationLabel(): string
    {
        return __('filament-panels.question.title');
    }

    /**
     *
     * Retrieves questions assigned to the current tutor and categorizes them into:
     * - All questions 
     * - Answered questions 
     * - Unanswered questions 
     *
     *
     * @return array Array containing categorized questions for the view
     */
    public function getViewData(): array
    {
        // Get the current tutor's ID from the authenticated user
        $tutorId = Tutor::where('user_id', Auth::id())->value('id');

        // Create a base query for all questions assigned to this tutor, ordered by most recent
        $questions = Question::where('tutor_id', $tutorId)->latest();

        return [
            // All questions assigned to this tutor
            'allQuestions' => (clone $questions)->get(),

            // Only questions that have been answered 
            'answeredQuestions' => (clone $questions)->whereNotNull('answer_text')->get(),

            // Only questions that haven't been answered yet 
            'unansweredQuestions' => (clone $questions)->whereNull('answer_text')->get(),
        ];
    }
}
