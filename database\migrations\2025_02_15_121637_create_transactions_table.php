<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::disableForeignKeyConstraints();

        Schema::create('transactions', function (Blueprint $table) {
            $table->id();
            $table->string('email', 255);
            $table->string('phone', 20);
            $table->foreignId('subscription_id')->constrained();
            $table->enum('payment_provider', ["TLYNC","STRIPE"]);
            $table->decimal('amount', 10, 2);
            $table->enum('currency', ["USD","EUR","LYD"]);
            $table->enum('status', ["PENDING","COMPLETED","FAILED"])->default('PENDING');
            $table->uuid('uuid')->unique();
            $table->timestamps();
        });

        Schema::enableForeignKeyConstraints();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
