name: Auto deploy addrus-pay to AWS

on:
  push:
    branches: [development] # Trigger on push to the "development" branch.

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy to Server
        uses: appleboy/ssh-action@v1
        with:
          host: ${{ secrets.SERVER_IP }}
          username: ${{ secrets.SSH_USERNAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |

            # Install rsync if missing (Debian/Ubuntu)
            sudo apt update && sudo apt install -y rsync

            # Navigate to the project directory
            cd /home/<USER>/projects/addrus-pay

            # Pull the latest code and reset to the development branch

            # temporary changing remote url so we use SSH instead of HTTPS.
            sudo -u bitnami git -c url."**************:".insteadOf="https://github.com/" fetch --all
            sudo -u bitnami git reset --hard origin/development

            # Install any new composer dependencies
            composer install --no-dev --prefer-dist --no-interaction

            #Install NPM dependencies
            npm ci --omit=dev

            #Build assets
            npm run build

            # Clear Laravel caches
            php artisan optimize:clear

            # Clear & re-cash filament
            php artisan filament:optimize-clear
            php artisan filament:optimize

            # Run database migrations
            php artisan migrate --force

            # Sync the public directory (exclude specific files)
            sudo rsync -avz \
              --exclude='index.php' \
              --exclude='.htaccess' \
              --exclude='robots.txt' \
              /home/<USER>/projects/addrus-pay/public/ \
              /home/<USER>/htdocs/

            # Restart Apache
            sudo /opt/bitnami/apache/bin/httpd -k restart
