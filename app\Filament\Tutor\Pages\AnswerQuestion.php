<?php

namespace App\Filament\Tutor\Pages;

use App\Enums\AnswerType;
use App\Models\AskMe\Question;
use App\Services\TutorService;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Storage;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

/**
 * AnswerQuestion Page
 *
 * This page allows tutors to answer student questions 
*/
class AnswerQuestion extends Page implements HasForms
{
    use InteractsWithForms;
    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static bool $shouldRegisterNavigation = false;
    protected static ?string $title = ' ';
    public array $data = [];
    protected static string $view = 'filament.pages.answer-question';
    public ?Question $question = null;


    /**
     * Define the dynamic route pattern for this page
     * 
     */
    public static function getSlug(): string
    {
        return 'answer-question/{record}';
    }

    /**
     * Initialize the page with the question data
     *
     * It sets up the question  and initializes the form data
     * with any existing answer text.
     *
     * @param Question $record The question being answered
     * @return void
     */
    public function mount(Question $record): void
    {
        $this->question = $record;
        $this->data = [
            'answer_text' => $record->answer_text,
        ];
    }

    /**
     * Define the form structure for answering questions
     * 
     * @param Form $form The form instance
     * @return Form The configured form
     */
    public function form(Form $form): Form
    {
        return $form->statePath('data')
            ->schema([
                // Text area for the tutor's written answer
                Textarea::make('answer_text')
                    ->label(__('filament-panels.question.fields.answer_text'))
                    ->required()
                    ->maxLength(1000),

                // File upload field for supporting materials
                FileUpload::make('answer_url')
                    ->label(__('filament-panels.question.action.upload'))
                    ->directory('answers')        // Store files in the 'answers' directory
                    ->disk('public')              // Use the public disk for storage
                    ->visibility('public')        // Make files publicly accessible
                    ->acceptedFileTypes([         // Limit to specific file types
                        'application/pdf',      
                        'video/mp4',             
                        'image/jpeg',             
                        'image/png',            
                        'image/gif'               
                    ])
                    ->previewable(true)           // Enable file preview
            ]);
    }


    /**
     * Process the form submission to answer a question
     *
     * This method handles:
     * - File upload processing 
     * - Detecting changes in the answer text or files
     * - Determining the answer type based on file extension
     * - Updating the question record with the answer
     * - Sending notifications to the student
     * - Recording tutor activity
     * - Redirecting back to the questions list
     *
     * @return void
     */
    public function submit(): void
    {
        $data = $this->data;
        $file = $data['answer_url'] ?? null;

        // Handle file upload processing
        if (is_array($file) && reset($file) instanceof TemporaryUploadedFile) {
            $uploadedFile = reset($file);

            // Delete old file if it exists
            if ($this->question->answer_url && Storage::disk('public')->exists(str_replace('/storage/', '', $this->question->answer_url))) {
                Storage::disk('public')->delete(str_replace('/storage/', '', $this->question->answer_url));
            }

            // Save new file to public storage
            $storedPath = $uploadedFile->store('answers', 'public');
            $data['answer_url'] = '/storage/' . $storedPath;
        } elseif ($file instanceof TemporaryUploadedFile) {

            // Delete old file if it exists
            if ($this->question->answer_url && Storage::disk('public')->exists(str_replace('/storage/', '', $this->question->answer_url))) {
                Storage::disk('public')->delete(str_replace('/storage/', '', $this->question->answer_url));
            }

            // Save new file to public storage
            $storedPath = $file->store('answers', 'public');
            $data['answer_url'] = '/storage/' . $storedPath;
        } else {
            // No new file uploaded, keep existing file path
            $data['answer_url'] = $this->question->answer_url;
        }

        // Check if there are any changes to save
        $newText = trim($data['answer_text'] ?? '');
        $oldText = trim($this->question->answer_text ?? '');
        $oldUrl = $this->question->answer_url;

        $isTextChanged = $newText !== $oldText;
        $isFileChanged = $oldUrl !== $data['answer_url'];

        // If nothing changed, show notification and exit
        if (!$isTextChanged && !$isFileChanged) {
            Notification::make()
                ->title(__('filament-panels.question.notification.no_change'))
                ->info()
                ->send();

            return;
        }

        // Determine answer type based on file extension if file changed
        if ($isFileChanged) {
            $extension = strtolower(pathinfo($data['answer_url'], PATHINFO_EXTENSION));
            // Map file extensions to appropriate AnswerType enum values
            $answerType = match ($extension) {
                'jpg', 'jpeg', 'png', 'gif' => AnswerType::Image,
                'mp4'                       => AnswerType::Video,
                'pdf'                       => AnswerType::Document,
                default                     => null,
            };
        } else {
            // Keep existing answer type if file didn't change
            $answerType = $this->question->answer_type;
        }

        // Update the question record with the new answer data
        $updated = $this->question->update([
            'answer_text' => $data['answer_text'] ?? null,
            'answer_url'  => $data['answer_url'] ?? null,
            'answer_type' => $answerType,
        ]);

        // Show success or error notification
        Notification::make()
            ->title($updated ? __('filament-panels.question.notification.success') : __('filament-panels.question.notification.error'))
            ->{$updated ? 'success' : 'danger'}()
            ->duration(3000)
            ->send();

        if ($updated) {
            // Record tutor activity for this answer
            app(TutorService::class)->tutorActivities($this->question);

            // Send notification to the student about the new answer
            app(TutorService::class)->sendAnswerTOStudent($this->question->student->number, $this->question);

            // Redirect back to the tutor questions list
            $this->redirect('/tutor/tutor-questions');
        }
    }
}
