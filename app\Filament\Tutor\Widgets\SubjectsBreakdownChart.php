<?php

namespace App\Filament\Tutor\Widgets;

use App\Models\AskMe\Question;
use App\Models\AskMe\Tutor;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

/**
 * SubjectsBreakdownChart Widget
 *
 * This widget displays a pie chart visualization of answered questions grouped by subject.

 */
class SubjectsBreakdownChart extends ChartWidget
{

    protected static ?string $heading = '';
    protected static ?int $sort = 2;


    /**
     * Get the localized heading for the widget
     *
     * @return string The localized widget heading
     */
    public function getHeading(): string
    {
        return __('filament-panels.question.fields.subject');
    }

    /**
     *
     * This method:
     * - Retrieves the current tutor's ID
     * - Queries answered questions grouped by subject
     * - Formats the data for the chart library
     *- Assigns colors to each subject segment
     *
     * @return array Chart data structure with datasets and labels
     */
    protected function getData(): array
    {
        // Get the current authenticated user's ID
        $userId = Auth::user()->id;

        // Find the tutor record associated with this user
        $tutor = Tutor::where('user_id', $userId)->first();

        // Query for answered questions grouped by subject
        $data = Question::where('tutor_id', $tutor->id)
            ->whereNotNull('answer_text')  // Only include answered questions
            ->select('subject_id', DB::raw('count(*) as total'))  // Count questions per subject
            ->groupBy('subject_id')  
            ->with('subject')  
            ->get();

        $labels = [];  // Will hold subject names
        $values = [];  // Will hold question counts

        // Process query results into chart data
        foreach ($data as $row) {
            // Use subject name or 'Unknown' if relationship is missing
            $labels[] = $row->subject->name ?? 'Unknown';
            // Add the count of questions for this subject
            $values[] = $row->total;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Questions Answered',
                    'data' => $values,
                    // Color palette for the pie chart segments
                    'backgroundColor' => [
                        '#6366F1',  
                        '#06B6D4',  
                        '#10B981', 
                        '#84CC16', 
                        '#F59E0B', 
                        '#EF4444', 
                        '#8B5CF6', 
                        '#EC4899',  
                        '#F97316', 
                        '#3B82F6',  
                    ],
                ],
            ],
            'labels' => $labels,
        ];
    }


    protected function getType(): string
    {
        return 'pie';
    }
}
