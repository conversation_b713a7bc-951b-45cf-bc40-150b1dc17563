<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class AddToCatelog implements ShouldQueue
{
    use Queueable;

    public $payload;


    public function __construct( $payload,)
    {
        $this->payload = $payload;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $url = config('api.whatsapp_api_url') . '661190839776792/products';
        Log::info("usrl " . $url);
        $response = Http::withToken(config('api.whatsapp_access_token'))
            ->post($url, $this->payload);
        Log::info("Product added to catelog: " . json_encode($response->json()));
    }
}
