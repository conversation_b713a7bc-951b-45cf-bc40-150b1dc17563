<?php

namespace App\Models\AskMe;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Student extends Model
{
    protected $connection = 'mysql_secondary';

    protected $fillable = ['name', 'number','status'];
    protected $casts = [
        'id' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
    public function subscriptions(): Has<PERSON>any { return $this->hasMany(Subscription::class); }
    public function questions(): HasMany { return $this->hasMany(Question::class); }
    public function comments(): Has<PERSON><PERSON> { return $this->hasMany(StudentComment::class); }
    public function transactions(): Has<PERSON><PERSON> { return $this->hasMany(Transaction::class); }}
