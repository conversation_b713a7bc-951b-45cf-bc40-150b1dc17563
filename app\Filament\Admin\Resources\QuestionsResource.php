<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\QuestionsResource\Pages;
use App\Models\AskMe\Question;
use Filament\Forms\Form;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ViewEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

/**
 * Questions Resource
 *
 * Manages student questions in the admin panel.
 */
class QuestionsResource extends Resource
{
    /**
     * The model that this resource manages
     */
    protected static ?string $model = Question::class;
    protected static ?string $navigationIcon = 'heroicon-o-question-mark-circle';

    /**
     * Get the navigation badge for this resource
     *
     * Displays the total count of questions in the sidebar
     *
     * @return string|null The badge text
     */
    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }

    /**
     * Get the title for this resource page
     *
     * @return string The title
     */
    public function getTitle(): string
    {
        return __('filament-panels.question.plural');
    }

    /**
     * Get the singular label for this resource
     *
     * @return string The localized singular label
     */
    public static function getModelLabel(): string
    {
        return __('filament-panels.question.singular');
    }

    /**
     * Get the plural label for this resource
     *
     * @return string The localized plural label
     */
    public static function getPluralModelLabel(): string
    {
        return __('filament-panels.question.plural');
    }

    /**
     * Get the navigation label for this resource
     *
     * @return string The localized navigation label
     */
    public static function getNavigationLabel(): string
    {
        return __('filament-panels.question.title');
    }
    /**
     * This resource primarily uses the view-only mode, so the form is empty
     *
     * @param Form $form The Filament form instance
     * @return Form The configured form
     */
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // No form fields defined as this resource is primarily for viewing
            ]);
    }

    /**
     * Define the table structure for displaying questions
     *
     * @param Table $table The Filament table instance
     * @return Table The configured table
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                // Question ID column
                TextColumn::make('id')
                    ->label('#')
                    ->sortable()
                    ->searchable(),

                // Question image column
                ImageColumn::make('image_url')
                    ->label(__('filament-panels.question.fields.question_image'))
                    ->getStateUsing(function ($record) {
                        $path = data_get($record, 'image_url');
                        return asset($path);
                    })
                    ->placeholder(
                        __('filament-panels.question.fields.no_image')
                    ),

                // Student name column
                TextColumn::make('student.name')
                    ->label(__('filament-panels.question.fields.name'))
                    ->sortable()
                    ->searchable(),

                // Question text column
                TextColumn::make('question_text')
                    ->label(__('filament-panels.question.fields.question_text'))
                    ->searchable(),

                // Answer text column with status indicators
                TextColumn::make('answer_text')
                    ->label(__('filament-panels.question.fields.answer_text'))
                    ->getStateUsing(function ($record) {
                        // Determine the appropriate text based on answer status
                        if ($record->answer_text === null && $record->answer_url === null) {
                            $answer = __('filament-panels.question.fields.not_answered');
                        } elseif ($record->answer_text === null) {
                            $answer = __('filament-panels.question.fields.no_answer_text');
                        } else {
                            $answer = $record->answer_text;
                        }
                        return $answer;
                    })
                    ->badge() // Display as a badge
                    ->color(fn($record) => match (true) {
                        // Color coding based on answer status
                        $record->answer_text === null && $record->answer_url === null => 'danger', // Not answered
                        $record->answer_text === null => 'warning', // Only has uploaded answer
                        default => 'success', // Has text answer
                    }),

                // Creation date column
                TextColumn::make('created_at')
                    ->label(__('filament-panels.question.fields.created_at'))
                    ->dateTime()
                    ->sortable(),

                // Last update date column
                TextColumn::make('updated_at')
                    ->label(__('filament-panels.question.fields.updated_at'))
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([]) // No filters defined
            ->actions([
                // Add view action to each row
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                // Bulk actions group
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
    /**
     * Customize the Eloquent query for this resource
     *
     * Eager loads the tutor relationship with their activities
     *
     * @return Builder The modified query
     */
    public static function getEloquentQuery(): \Illuminate\Database\Eloquent\Builder
    {
        return parent::getEloquentQuery()->with('tutor.tutorActivites');
    }

    /**
     * Define the infolist structure for the detailed view of a question
     *
     * @param Infolist $infolist The Filament infolist instance
     * @return Infolist The configured infolist
     */
    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                // Question details section
                Section::make('')
                    ->schema([
                        // Student name who asked the question
                        TextEntry::make('student.name')
                            ->label(__('filament-panels.tutor.fields.name')),

                        // When the question was created
                        TextEntry::make('created_at')
                            ->label(__('filament-panels.question.fields.created_at'))
                            ->dateTime(),

                        // The question text
                        TextEntry::make('question_text')
                            ->label(__('filament-panels.question.fields.question_text')),

                        // Question image (if any)
                        ImageEntry::make('image_url')
                            ->label(__('filament-panels.question.fields.question_image'))
                            ->getStateUsing(function ($record) {
                                $path = data_get($record, 'image_url');
                                return asset($path);
                            })
                            ->hidden(fn($record) => empty($record->image_url)),
                    ]),

                // Answer section
                Section::make('')
                    ->schema([
                        // Text answer (if any)
                        TextEntry::make('answer_text')
                            ->label(__('filament-panels.question.fields.answer_text'))
                            ->getStateUsing(function ($record) {
                                if (!$record->answer_text && !$record->answer_url) {
                                    return __('filament-panels.question.fields.not_answered');
                                } else {
                                    return $record->answer_text;
                                }
                            })
                            ->hidden(fn($record) => empty($record->answer_text)),

                        // Uploaded answer media (if any)
                        ViewEntry::make('answer_url')
                            ->label(__('filament-panels.question.fields.uploaded_answer'))
                            ->view('filament.infolists.media-entry')
                            ->getStateUsing(fn($record) => $record->answer_url ? asset($record->answer_url) : null)
                            ->hidden(fn($record) => empty($record->answer_url)),
                    ]),

                // Tutor activity log section
                Section::make('')
                    ->schema([
                        // Activity log for the question
                        ViewEntry::make('tutorActivites')
                            ->label('Activity Log')
                            ->view('filament.infolists.tutor-activities-log')
                            ->columnSpanFull(),
                    ])
            ]);
    }


    public static function getRelations(): array
    {
        return [
        ];
    }

    /**
     * Define the pages for the Question resource
     *
     * @return array The pages configuration
     */
    public static function getPages(): array
    {
        return [
            // List page (main index view)
            'index' => Pages\ListQuestions::route('/'),
        ];
    }
}
