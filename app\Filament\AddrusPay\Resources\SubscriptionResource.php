<?php

namespace App\Filament\AddrusPay\Resources;

use App\Enums\SubscriptionPlanType;
use App\Filament\AddrusPay\Resources\SubscriptionResource\Pages;
use App\Filament\AddrusPay\Resources\SubscriptionResource\RelationManagers;
use App\Models\Subscription;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SubscriptionResource extends Resource
{
    protected static ?string $model = Subscription::class;

    public static function getNavigationIcon(): string
    {
        return 'heroicon-o-credit-card';
    }

    public static function getModelLabel(): string
    {
        return __('Subscription');
    }

    public static function getPluralModelLabel(): string
    {
        return __('Subscriptions');
    }

    public static function getNavigationLabel(): string
    {
        return static::getPluralModelLabel();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('email')
                    ->label(__('email'))
                    ->searchable()
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('status')
                    ->label(__('status'))
                    ->badge()
                    ->color(fn (bool $state): string => $state ? 'success' : 'info')
                    ->formatStateUsing(fn (bool $state): string => $state ? 'مستخدم' : 'غير مستخدم')
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('subscriptionPlan.title')
                    ->label(__('subscriptionPlan'))
                    ->searchable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('subscriptionPlan.type')
                    ->label(__('subscriptionPlanType'))
                    ->badge()
                    ->formatStateUsing(fn (SubscriptionPlanType $state): string => $state->label())
                    ->toggleable(),
                Tables\Columns\TextColumn::make('subscriptionPlan.price')
                    ->label(__('subscriptionPlanPrice'))
                    ->formatStateUsing(fn(string $state): string => $state . ' د.ل')
                    ->toggleable(),
                Tables\Columns\TextColumn::make('subscriptionPlan.period')
                    ->label(__('subscriptionPlanPeriod'))
                    ->badge()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                //     Tables\Actions\DeleteBulkAction::make(),
                // ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSubscriptions::route('/'),
            'create' => Pages\CreateSubscription::route('/create'),
            'edit' => Pages\EditSubscription::route('/{record}/edit'),
        ];
    }

    public static function canCreate(): bool
    {
        return false;
    }

    public static function canEdit(Model $record): bool
    {
        return false;
    }

    public static function canDelete(Model $record): bool
    {
        return false;
    }
}
