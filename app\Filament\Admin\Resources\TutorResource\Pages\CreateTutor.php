<?php

namespace App\Filament\Admin\Resources\TutorResource\Pages;

use App\Filament\Admin\Resources\TutorResource;
use App\Models\AskMe\User;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Hash;

class CreateTutor extends CreateRecord
{
    protected static string $resource = TutorResource::class;

    /**
     * This method allows to modify the form data
     * before it's used to create a new <PERSON>tor record.
     */
    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $email= $data['user']['email'];
        // Extract user fields
        $userData = [
            'name' => $data['user']['name'],
            'email' => $email . '@askme.com',
            'password' => Hash::make($this->form->getRawState()['user']['password']),
        ];
        // Create a new User 
        $user = User::create($userData);

        // Assign the new user's ID to the Tutor's foreign key
        $data['user_id'] = $user->id;

        // Remove the nested 'user' array 
        unset($data['user']);

        return $data;
    }
}
