<?php

namespace App\Filament\Admin\Pages;

use Filament\Pages\Page;

/**
 * Reports Page for the Admin Panel
 *
 * This page allows administrators to generate different types of reports
 * including tutor reports, revenue reports, and subscription reports.
 * Reports can be filtered by time range (week, month, year).
 */
class Reports extends Page
{
    // Icon displayed in the navigation menu
    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    // The Blade view that renders this page
    protected static string $view = 'filament.admin.pages.reports';

    // The title displayed in the admin panel
    protected static ?string $title = 'تقارير النظام';

    public string $reportType = 'tutors';
    public string $range = 'week';       
    public string $selectedMonth = '';    
    public array $availableMonths = [];   
    public string $pdfUrl = '';           

    protected static bool $isLazy = false;
    protected static bool $hasDynamicProperties = true;

    /*
     * If the selected range is month, generate the list of available months
     * for the user to select from.
     */
    public function mount()
    {
        if ($this->range === 'month') {
            $this->generateAvailableMonths();
        }
    }

    /**
     * Update the time range for the report
     *
     * This method is called when the user changes the time range dropdown.
     * If month is selected, it generates the list of available months.
     * Otherwise, it clears any month-specific selections.
     *
     * @param string $value The selected range value ('week', 'month', or 'year')
     */
    public function updateRange($value)
    {
        $this->range = $value;

        if ($value === 'month') {
            $this->generateAvailableMonths();
        } else {
            $this->selectedMonth = '';
            $this->availableMonths = [];
        }
    }

    /**
     * Generate a list of available months for the current year
     */
    public function generateAvailableMonths()
    {
        $currentMonth = now()->month;
        $this->availableMonths = [];

        for ($i = 1; $i <= $currentMonth; $i++) {
            $this->availableMonths[$i] = now()->startOfYear()->addMonths($i - 1)->translatedFormat('F');
        }
    }

    /**
     * Generate the PDF report based on selected 
     *
     */
    public function generateReport()
    {
        $this->pdfUrl = route('admin.reports.dynamic.pdf', [
            'type' => $this->reportType,
            'range' => $this->range,
            'month' => $this->range === 'month' ? $this->selectedMonth : null,
        ]);
    }

    /**
     * Get the localized label for the selected time range
     *
     * @return string The localized label for the current range
     */
    public function getRangeLabel(): string
    {
        return match ($this->range) {
            'week' => 'الأسبوع',
            'year' => 'السنة',
            default => 'الشهر',
        };
    }
}
