<?php

namespace App\Filament\AddrusPay\Widgets;

use App\Models\Subscription;
use App\Enums\SubscriptionPlanType;
use App\Models\SubscriptionPlan;
use Filament\Support\Colors\Color;
use Filament\Widgets\ChartWidget;

class SubscriptionTypesChartWidget extends ChartWidget
{
    protected static ?int $sort = 3;

    protected static ?string $heading = 'توزيع أنواع الاشتراكات';

    protected function getData(): array
    {
        // Get counts for each subscription plan type
        $localCount = SubscriptionPlan::where('type', SubscriptionPlanType::LOCAL->value)->count();
        $internationalCount = SubscriptionPlan::where('type', SubscriptionPlanType::INTERNATIONAL->value)->count();

        return [
            'datasets' => [
                [
                    'label' => 'أنواع الاشتراكات',
                    'data' => [
                        $localCount,
                        $internationalCount,
                    ],
                    'backgroundColor' => [
                        'rgb(' . Color::Blue[400] . ')',    // محلي
                        'rgb(' . Color::Purple[400] . ')',  // دولي
                    ],
                ],
            ],
            'labels' => [
                SubscriptionPlanType::LOCAL->label(),
                SubscriptionPlanType::INTERNATIONAL->label(),
            ],
        ];
    }

    protected function getType(): string
    {
        return 'doughnut';
    }

    protected function getOptions(): array
    {
        return [
            'scales' => [
                'y' => [
                    'display' => false,
                ],
                'x' => [
                    'display' => false,
                ],
            ],
        ];
    }

    public static function canView(): bool
    {
        return false;
    }
}