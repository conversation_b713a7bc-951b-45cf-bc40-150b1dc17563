<?php


namespace App\Filament\Admin\Pages;

use App\Enums\WhatsAppMessageType;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;

class WhatsAppSettingsTab
{
    /**
     * Get the WhatsApp settings tab configuration
     *
     * Creates a tab with form components for WhatsApp related settings
     *
     * @return Tab The configured tab component
     */
    public static function getTab(): Tab
    {
        return
            Tab::make(__('filament-panels.setting.whatsApp_setting'))
                ->schema([
                    Group::make()
                        ->schema([
                            // Toggle for requiring subscription
                            Toggle::make('subscription_required')
                                ->label(__('filament-panels.setting.subscription_required'))
                                ->helperText(__('filament-panels.setting.subscription_required_desc'))
                                ->default(true),

                            // Question limit setting (only visible when subscription is not required)
                            TextInput::make('question_limit')
                                ->label(__('filament-panels.setting.question_limit'))
                                ->hint('-1 means unlimited questions per day')
                                ->numeric()
                                ->visible(fn($get) => !$get('subscription_required'))
                                ->default(3)
                                ->helperText(__('filament-panels.setting.question_limit_desc')),

                            // General welcome message template
                            Textarea::make(WhatsAppMessageType::WELCOME->value)
                                ->label(__('filament-panels.setting.whatsApp_welcome'))
                                ->rows(2)
                                ->helperText(__('filament-panels.setting.whatsApp_welcome_desc')),

                            // Welcome message for trial users
                            Textarea::make(WhatsAppMessageType::WELCOME_TRIAL->value)
                                ->label(__('filament-panels.setting.whatsApp_welcome_free'))
                                ->rows(2)
                                ->helperText(__('filament-panels.setting.whatsApp_welcome_free_desc')),

                            // Welcome message for subscription users
                            Textarea::make(WhatsAppMessageType::WELCOME_SUBSCRIPTION->value)
                                ->label(__('filament-panels.setting.whatsApp_welcome_sub'))
                                ->rows(2)
                                ->helperText(__('filament-panels.setting.whatsApp_welcome_sub')),

                            // Welcome message for tutors
                            Textarea::make(WhatsAppMessageType::WELCOME_TUTOR->value)
                                ->label(__('filament-panels.setting.whatsApp_welcome_tutor'))
                                ->rows(2)
                                ->helperText(__('filament-panels.setting.whatsApp_welcome_tutor_desc')),
                        ])
                        // Set to single column layout 
                        ->columns(1),
                        ]);

    }

    /**
     * Get the sort order for this tab
     *
     *
     * @return int The sort order value
     */
    public static function getSortOrder(): int
    {
        return 1; 
    }
}
