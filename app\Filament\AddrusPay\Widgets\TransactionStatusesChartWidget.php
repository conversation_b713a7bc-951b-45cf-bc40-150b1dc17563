<?php

namespace App\Filament\AddrusPay\Widgets;

use App\Models\Transaction;
use App\Enums\TransactionStatus;
use Filament\Widgets\ChartWidget;

class TransactionStatusesChartWidget extends ChartWidget
{
    protected static ?int $sort = 5;

    protected static ?string $heading = 'توزيع حالات المعاملات';

    protected function getData(): array
    {
        $colorMap = [
            TransactionStatus::PENDING->value => 'rgb(255, 193, 7)',  // Material Amber 500
            TransactionStatus::COMPLETED->value => 'rgb(76, 175, 80)', // Material Green 500
            TransactionStatus::FAILED->value => 'rgb(244, 67, 54)',   // Material Red 500
            TransactionStatus::CANCELED->value => 'rgb(158, 158, 158)', // Material Gray 500
        ];

        $statusCounts = [
            TransactionStatus::PENDING->value => Transaction::where('status', TransactionStatus::PENDING->value)->count(),
            TransactionStatus::COMPLETED->value => Transaction::where('status', TransactionStatus::COMPLETED->value)->count(),
            TransactionStatus::FAILED->value => Transaction::where('status', TransactionStatus::FAILED->value)->count(),
            TransactionStatus::CANCELED->value => Transaction::where('status', TransactionStatus::CANCELED->value)->count(),
        ];

        $statusLabels = [
            TransactionStatus::PENDING->value => TransactionStatus::PENDING->label(),
            TransactionStatus::COMPLETED->value => TransactionStatus::COMPLETED->label(),
            TransactionStatus::FAILED->value => TransactionStatus::FAILED->label(),
            TransactionStatus::CANCELED->value => TransactionStatus::CANCELED->label(),
        ];

        // Filter out statuses with zero count for cleaner chart
        $data = [];
        $labels = [];
        $colors = [];

        foreach ($statusCounts as $status => $count) {
            if ($count > 0) {
                $data[] = $count;
                $labels[] = $statusLabels[$status];
                $colors[] = $colorMap[$status];
            }
        }

        return [
            'datasets' => [
                [
                    'label' => 'حالات المعاملات',
                    'data' => $data,
                    'backgroundColor' => $colors,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'pie';
    }

    protected function getOptions(): array
    {
        return [
            'scales' => [
                'y' => [
                    'display' => false,
                ],
                'x' => [
                    'display' => false,
                ],
            ],
        ];
    }
}