<?php

namespace App\Filament\Tutor\Pages;

use App\Models\AskMe\Grade;
use App\Models\AskMe\Subject;
use App\Models\AskMe\Tutor;
use App\Models\AskMe\TutorSubject;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

/**
 * TutorSettings Page
 *
 * This page allows tutors to manage their account settings, including:
 * - Subjects and grade they can teach
 * - Account activation status
 * - Password changes
 */

class TutorSettings extends Page
{
   
    protected static ?string $navigationIcon = 'heroicon-c-cog-8-tooth';
    protected static string $view = 'filament.pages.tutor-settings';
    protected static ?string $title = '';
    protected static bool $shouldRegisterNavigation = true;
    public array $data = [];


    /**
     * Get the title for the page
     *
     * @return string The localized page title
     */
    public function getTitle(): string
    {
        return __('filament-panels.setting.plural');
    }

    /**
     * Get the singular model label for the page
     *
     * @return string The localized singular model label
     */
    public static function getModelLabel(): string
    {
        return __('filament-panels.setting.singular');
    }

    /**
     * Get the plural model label for the page
     *
     * @return string The localized plural model label
     */
    public static function getPluralModelLabel(): string
    {
        return __('filament-panels.setting.plural');
    }

    /**
     * Get the navigation label for the sidebar
     *
     * @return string The localized navigation label
     */
    public static function getNavigationLabel(): string
    {
        return __('filament-panels.setting.title');
    }

    /**
     * Initialize the page with the tutor's current settings
     *
     * It retrieves the tutor's current subjects, grades, and activation status
     * to the form.
     *
     * @return void
     */
    public function mount(): void
    {
        // Get the current tutor's ID from the authenticated user
        $tutorId = Tutor::where('user_id', Auth::id())->value('id');

        // Retrieve the tutor with their subject relationships
        $tutor = Tutor::where("id", $tutorId)->with('tutorSubjects')->first();

        // Initialize the form data with the tutor's current settings
        $this->data = [
            // Map the tutor's subjects and grades to the format expected by the repeater
            'tutorSubjects' => $tutor->tutorSubjects->map(fn($ts) => [
                'subject_id' => $ts->subject_id,
                'grade_id' => $ts->grade_id,
            ])->toArray(),

            // Set the active status checkbox based on the user's is_active flag
            'is_active' => $tutor->user->is_active === 1 ? true : false,
        ];
    }

    /**
     * Define the form structure for tutor settings
     * @param Form $form The form instance
     * @return Form The configured form
     */
    public function form(Form $form): Form
    {
        return $form
            ->statePath('data')
            ->schema([
                // Subjects and grades fieldset
                Fieldset::make(__('filament-panels.setting.subjects'))
                    ->schema([
                        // Repeater for multiple subject-grade combinations
                        Repeater::make('tutorSubjects')
                            ->label(__('filament-panels.setting.subjects'))
                            ->schema([
                                // Subject selection dropdown
                                Select::make('subject_id')
                                    ->label(__('filament-panels.subject.singular'))
                                    ->options(Subject::all()->pluck('name', 'id'))
                                    ->searchable()
                                    ->required(),

                                // Grade level selection dropdown
                                Select::make('grade_id')
                                    ->label(__('filament-panels.grade.singular'))
                                    ->options(Grade::all()->pluck('name', 'id'))
                                    ->searchable()
                                    ->required(),
                            ])
                    ]),

                // Account activation toggle
                Checkbox::make('is_active')
                    ->label(__('filament-panels.setting.active')),

                // Password change fieldset
                Fieldset::make(__('filament-panels.setting.password'))
                    ->schema([
                        // New password field
                        TextInput::make('password')
                            ->label(__('filament-panels.setting.password'))
                            ->password()
                            ->minLength(6)
                            ->maxLength(50)
                            ->confirmed()
                            ->nullable(),

                        // Password confirmation field
                        TextInput::make('password_confirmation')
                            ->label(__('filament-panels.setting.password_confirm'))
                            ->password()
                            ->same('password')
                            ->nullable(),
                    ]),
            ]);
    }

    /**
     * Process the form submission to save tutor settings
     *
     * This method handles:
     * - Updating the tutor's account activation status
     * - Changing the password if provided
     * - Updating the tutor's subjects and grade levels
     *
     * @return void
     */
    public function submit(): void
    {
        // Get the authenticated user
        $user = Auth::user();

        // Retrieve the tutor with their relationships
        $tutor = Tutor::where('user_id', $user->id)
            ->with('tutorSubjects')
            ->with('user')
            ->firstOrFail();

        // Get the submitted form data
        $data = $this->form->getState();

        // Update activation status
        $tutor->user->is_active = $data['is_active'] ?? false;

        // Only update password if it's filled
        if (!empty($data['password'])) {
            $tutor->user->password = Hash::make($data['password']);
        }

        // Save user changes
        $tutor->user->save();

        // Process tutorSubjects from repeater
        // Convert new subject-grade combinations to strings for comparison
        $newCombinations = collect($data['tutorSubjects'] ?? [])
            ->map(fn($row) => $row['grade_id'] . '-' . $row['subject_id'])
            ->toArray();

        // Get existing subject-grade combinations as strings
        $existingCombinations = $tutor->tutorSubjects->map(function ($item) {
            return $item->grade_id . '-' . $item->subject_id;
        })->toArray();

        // Check if there are any differences between new and existing combinations
        if (array_diff($newCombinations, $existingCombinations) || array_diff($existingCombinations, $newCombinations)) {
            // Delete all existing subject-grade relationships
            TutorSubject::where('tutor_id', $tutor->id)->delete();

            // Create new subject-grade relationships
            foreach ($data['tutorSubjects'] ?? [] as $row) {
                TutorSubject::create([
                    'tutor_id' => $tutor->id,
                    'grade_id' => $row['grade_id'],
                    'subject_id' => $row['subject_id'],
                ]);
            }
        }

        // Show success notification
        Notification::make()
            ->title(__('filament-panels.setting.seved'))
            ->success()
            ->send();
    }
}
