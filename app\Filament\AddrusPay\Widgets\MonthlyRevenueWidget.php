<?php

namespace App\Filament\AddrusPay\Widgets;

use App\Enums\SubscriptionPlanType;
use App\Models\Transaction;
use App\Enums\TransactionStatus;
use Carbon\Carbon;
use Filament\Widgets\ChartWidget;

class MonthlyRevenueWidget extends ChartWidget
{
    protected static ?int $sort = 2;

    protected int | string | array $columnSpan = 'full';
    protected static ?string $heading = 'اتجاه الإيرادات الشهرية';

    protected function getData(): array
    {
        // Get data for the last 6 months
        $revenueData = [];
        $transactionCountData = [];
        $labels = [];

        for ($i = 11; $i >= 0; $i--) {
            $month = Carbon::now()->subMonths($i);
            $labels[] = $month->translatedFormat('M Y'); // Use translated format for Arabic months if needed

            // Get total revenue for the month (only COMPLETED transactions)
            $revenue = Transaction::whereMonth('created_at', $month->month)
                ->whereYear('created_at', $month->year)
                ->where('status', TransactionStatus::COMPLETED->value)
                ->whereHas('subscription', function($query) {
                    $query->whereHas('subscriptionPlan', function($query) {
                        $query->where('type', SubscriptionPlanType::LOCAL);
                    });
                })
                ->sum('amount');

            $revenueData[] = $revenue;

            // Count of transactions for the month
            $transactionCount = Transaction::whereMonth('created_at', $month->month)
                ->whereYear('created_at', $month->year)
                ->whereHas('subscription', function($query) {
                    $query->whereHas('subscriptionPlan', function($query) {
                        $query->where('type', SubscriptionPlanType::LOCAL);
                    });
                })
                ->count();

            $transactionCountData[] = $transactionCount;
        }

        return [
            'datasets' => [
                [
                    'label' => '(اشتراكات محلية) الإيرادات الشهرية',
                    'data' => $revenueData,
                    'borderColor' => 'rgb(59, 130, 246)',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.2)',
                    'fill' => 'start',
                    'tension' => 0.3,
                ],
                [
                    'label' => '(اشتراكات محلية) عدد المعاملات',
                    'data' => $transactionCountData,
                    'borderColor' => 'rgb(16, 185, 129)',
                    'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
                    'fill' => 'start',
                    'tension' => 0.3,
                    'yAxisID' => 'y1',
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'scales' => [
                'y' => [
                    'title' => [
                        'display' => true,
                        'text' => 'الإيرادات (د.ل)'
                    ],
                    'beginAtZero' => true,
                ],
                'y1' => [
                    'position' => 'right',
                    'title' => [
                        'display' => true,
                        'text' => 'عدد المعاملات'
                    ],
                    'beginAtZero' => true,
                    'grid' => [
                        'drawOnChartArea' => false,
                    ],
                ],
                'x' => [
                    'grid' => [
                        'display' => false,
                    ],
                ],
            ],
        ];
    }
}