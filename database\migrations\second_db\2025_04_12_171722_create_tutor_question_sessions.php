<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
     
        Schema::connection('mysql_secondary')->create('tutor_question_sessions', function (Blueprint $table) {
            $table->id();
            $table->string('whatsapp_number')->unique(); 
            $table->foreignId('question_id')->nullable()->constrained();
            $table->string('answer_text')->nullable();
            $table->string('answer_url')->nullable();
            $table->string('answer_id')->nullable();
            $table->enum('answer_type',['Image','Video','Document'])->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tutor_question_sessions');
    }
};
