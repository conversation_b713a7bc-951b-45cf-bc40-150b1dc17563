<?php

namespace App\Filament\Tutor\Pages;

use App\Models\AskMe\Question;
use Filament\Pages\Page;

/**
 * AnswerComments Page
 *
 * This page displays comments associated with answers provided by tutors.
 */
class AnswerComments extends Page
{

    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-bottom-center-text';
    protected static string $view = 'filament.pages.answer-comments';
    protected static bool $shouldRegisterNavigation = true;

    /**
     * Get the title for the page
     * Uses localized string for comments (plural form)
     *
     * @return string The localized page title
     */
    public function getTitle(): string
    {
        return __('filament-panels.comments.plural');
    }

    /**
     * Get the singular model label for the page
     *
     * @return string The localized singular model label
     */
    public static function getModelLabel(): string
    {
        return __('filament-panels.comments.singular');
    }

    /**
     * Get the plural model label for the page
     *
     * @return string The localized plural model label
     */
    public static function getPluralModelLabel(): string
    {
        return __('filament-panels.comments.plural');
    }

    /**
     * Get the navigation label for the sidebar
     *
     * @return string The localized navigation label
     */
    public static function getNavigationLabel(): string
    {
        return __('filament-panels.comments.title');
    }

    /**
     *
     * Retrieves questions that have comments
     * and orders them by most recent first.
     *
     * @return array Array containing questions with their comments
     */
    public function getViewData(): array
    {
        return [
            'questions' => Question::with('comments')
                ->whereHas('comments') // Only questions that have at least one comment
                ->latest()             // Most recent questions first
                ->get(),
        ];
    }
}
