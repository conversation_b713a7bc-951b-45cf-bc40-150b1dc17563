const interfaceTranslations = {
  selectedCountryAriaLabel: "선택한 국가",
  noCountrySelected: "선택한 국가가 없습니다.",
  countryListAriaLabel: "국가 목록",
  searchPlaceholder: "검색",
  zeroSearchResults: "검색 결과가 없습니다",
  oneSearchResult: "1개의 결과를 찾았습니다.",
  multipleSearchResults: "${count}개의 결과를 찾았습니다.",
  // additional countries (not supported by country-list library)
  ac: "어센션섬",
  xk: "코소보"
};
export default interfaceTranslations;
