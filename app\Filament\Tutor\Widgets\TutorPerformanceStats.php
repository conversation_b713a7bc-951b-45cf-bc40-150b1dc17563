<?php

namespace App\Filament\Tutor\Widgets;

use App\Enums\TutorActivities;
use App\Models\AskMe\Question;
use App\Models\AskMe\Tutor;
use App\Models\AskMe\TutorActivity;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * TutorPerformanceStats Widget
 *
 * This widget displays key performance metrics for the current tutor, including:
 * - Total number of questions answered
 * - Number of pending questions awaiting answers
 * - Average response time for answering questions
 *
 */
class TutorPerformanceStats extends BaseWidget
{

    protected static ?int $sort = 1;

    /**
     * Generate the statistics for display in the widget

     * @return array Array of Stat objects for display in the widget
     */
    protected function getStats(): array
    {
        // Get the current authenticated user
        $user = Auth::user();

        // Find the tutor record associated with this user
        $tutor = Tutor::where('user_id', $user->id)->first();

        // Calculate total questions answered 
        $answered = Question::where('tutor_id', $tutor->id)
            ->whereNotNull('answer_text')  
            ->count();

        // Calculate pending questions 
        $pending = Question::where('tutor_id', $tutor->id)
            ->whereNull('answer_text')  
            ->count();

        // Get all answer activities for this tutor to calculate average response time
        $answeredActivities = TutorActivity::where('tutor_id', $tutor->id)
            ->where('action', TutorActivities::Answer)  
            ->with('question') 
            ->get();

        $totalSeconds = 0; 
        $count = 0; 

        // Calculate total response time across all answered questions
        foreach ($answeredActivities as $activity) {
            // Only process activities with valid question and timestamps
            if ($activity->question && $activity->question->created_at) {
                // Calculate time difference between question creation and answer submission
                $diff = $activity->created_at->diffInSeconds($activity->question->created_at, false);

                // Ensure the difference is positive
                if ($diff < 0) {
                    $diff = abs($diff);
                }
                $totalSeconds += $diff;
                $count++;
            }
        }

        // Calculate average response time in minutes, rounded to 1 decimal place
        // If no valid responses, default to 0
        $avgResponseMinutes = $count > 0 ? round(($totalSeconds / $count) / 60, 1) : 0;

        return [
            // Total answered questions with success color
            Stat::make(__('filament-panels.dashboard.completed_count'), $answered)
                ->description(__('filament-panels.dashboard.completed_desc'))
                ->color('success'),

            // Pending questions with warning color
            Stat::make(__('filament-panels.dashboard.pending_count'), $pending)
                ->description(__('filament-panels.dashboard.pending_desc'))
                ->color('warning'),

            // Average response time with info color
            Stat::make(__('filament-panels.dashboard.answered_avg'), $avgResponseMinutes . ' د')
                ->description(__('filament-panels.dashboard.answered_avg_desc'))
                ->color('info'),
        ];
    }
}
