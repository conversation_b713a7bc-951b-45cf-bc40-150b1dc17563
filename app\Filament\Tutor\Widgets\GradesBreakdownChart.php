<?php

namespace App\Filament\Tutor\Widgets;

use App\Models\AskMe\Question;
use App\Models\AskMe\Tutor;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

/**
 * GradesBreakdownChart Widget
 *
 * This widget displays a pie chart visualization of answered questions grouped by grade level.
 */

class GradesBreakdownChart extends ChartWidget
{

    protected static ?string $heading = '';
    protected static ?int $sort = 2;
    /**
     * Get the localized heading for the widget
     *     *
     * @return string The localized widget heading
     */
    public function getHeading(): string
    {
        return __('filament-panels.question.fields.grade');
    }

    /**
     *
     * This method:
     * - Retrieves the current tutor's ID
     * - Queries answered questions grouped by grade level
     * = Formats the data for the chart 
     * - Assigns colors to each grade segment
     *
     * @return array Chart data structure with datasets and labels
     */
    protected function getData(): array
    {
        // Get the current authenticated user's ID
        $userId = Auth::user()->id;

        // Find the tutor record associated with this user
        $tutor = Tutor::where('user_id', $userId)->first();

        // Query for answered questions grouped by grade
        $data = Question::where('tutor_id', $tutor->id)
            ->whereNotNull('answer_text')  // Only include answered questions
            ->select('grade_id', DB::raw('count(*) as total'))  
            ->groupBy('grade_id') 
            ->with('grade') 
            ->get();

        $labels = [];  // Will hold grade names
        $values = [];  // Will hold question counts

        // Process query results into chart data
        foreach ($data as $row) {
            // Use grade name or 'Unknown' if relationship is missing
            $labels[] = $row->grade->name ?? 'Unknown';
            // Add the count of questions for this grade
            $values[] = $row->total;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Questions Answered',
                    'data' => $values,
                    // Color palette for the pie chart segments
                    'backgroundColor' => [
                        '#4CAF50', // green
                        '#2196F3', // blue
                        '#FFC107', // yellow
                        '#F44336', // red
                        '#9C27B0', // purple
                        '#00BCD4', // cyan
                        '#FF5722', // deep orange
                        '#795548', // brown
                    ],
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'pie';
    }
}
