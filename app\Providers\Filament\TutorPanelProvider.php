<?php

namespace App\Providers\Filament;

use App\Filament\Tutor\Pages\AnswerComments;
use App\Filament\Tutor\Pages\AnswerQuestion;
use App\Filament\Tutor\Pages\QuestionRequests;
use App\Filament\Tutor\Pages\TutorQuestions;
use App\Filament\Tutor\Pages\TutorSettings as PagesTutorSettings;
use App\Filament\Tutor\Pages\ViewAndAcceptQuestion;
use App\Filament\Tutor\Widgets\GradesBreakdownChart;
use App\Filament\Tutor\Widgets\SubjectsBreakdownChart;
use App\Filament\Tutor\Widgets\TutorPerformanceStats;
use App\Http\Middleware\SetLocale;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Navigation\MenuItem;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;

class TutorPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->default()
            ->id('tutor')
            ->path('tutor')
            ->authGuard('tutor')
            ->brandName('ادرس لتعليم الالكتروني')
            ->login()
            ->spa()
            ->unsavedChangesAlerts()
            ->colors([
                'primary' => Color::Blue,
            ])->userMenuItems([
                MenuItem::make()
                    ->label(fn(): string => app()->getLocale() === 'ar' ? 'English' : 'العربية')
                    ->url(fn(): string => '/switch-lang/' . (app()->getLocale() === 'ar' ? 'en' : 'ar'))
                    ->icon('heroicon-m-language'),
            ])
            ->discoverPages(in: app_path('Filament/Tutor/Pages'), for: 'App\\Filament\\Tutor\\Pages')
            ->discoverWidgets(in: app_path('Filament/Tutor/Widgets'), for: 'App\\Filament\\Tutor\\Widgets')
            ->pages([
                Pages\Dashboard::class,
                TutorQuestions::class,
                AnswerQuestion::class,
                AnswerComments::class,
                QuestionRequests::class,
                ViewAndAcceptQuestion::class,
                PagesTutorSettings::class
            ])
            ->widgets([
                TutorPerformanceStats::class,
                SubjectsBreakdownChart::class,
                GradesBreakdownChart::class,
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
                SetLocale::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ]);
    }
}
