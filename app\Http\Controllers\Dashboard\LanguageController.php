<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use  App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LanguageController extends Controller

{ 
    /**
     * Switch the language for the authenticated user.
     *
     * @param  string  $locale The language to switch to (en or ar)
     */
    public function switchLang(string $locale): RedirectResponse
    {
        if (! in_array($locale, ['en', 'ar'])) {
            abort(400);
        }
        $user = Auth::user();
        if ($user instanceof User) {
            $user->language = $locale;
            $user->save();
        }
        return back();
    }
}
