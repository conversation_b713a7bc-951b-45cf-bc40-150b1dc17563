<?php

namespace App\Filament\Tutor\Pages;

use App\Models\AskMe\Question;
use App\Models\AskMe\Tutor;
use App\Models\AskMe\TutorSubject;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Auth;

/**
 * QuestionRequests Page
 *
 * This page displays available question requests that match a tutor's subjects and grades.
 */
class QuestionRequests extends Page
{

    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static string $view = 'filament.pages.question-requests';
    protected static ?string $title = '';
    protected static bool $shouldRegisterNavigation = true;

    /**
     * Get the navigation badge count for the sidebar
     *
     * Calculates the number of available question requests that match the current
     * tutor's subjects and grades.
     * This count is displayed as a badge in the
     * navigation menu to indicate how many questions are available for the tutor
     * to accept.
     *
     * @return string|null The count of available questions as a string, or null if none
     */
    public static function getNavigationBadge(): ?string
    {
        // Get the current tutor's ID from the authenticated user
        $tutorId = Tutor::where('user_id', Auth::id())->value('id');

        // Get the tutor's subjects and grades they can teach
        $tutorSubjects = TutorSubject::where('tutor_id', $tutorId)
            ->get(['grade_id', 'subject_id']);

        $questions = Question::whereNull('tutor_id')
            ->where(function ($query) use ($tutorSubjects) {
                foreach ($tutorSubjects as $subject) {
                    $query->orWhere(function ($q) use ($subject) {
                        // Match both grade and subject for each of the tutor's qualifications
                        $q->where('grade_id', $subject->grade_id)
                            ->where('subject_id', $subject->subject_id);
                    });
                }
            })
            ->latest()
            ->count();

        return $questions;
    }
    /**
     * Get the title for the page
     *
     * @return string The localized page title
     */
    public function getTitle(): string
    {
        return __('filament-panels.question_request.plural');
    }

    /**
     * Get the singular model label for the page
     *
     * @return string The localized singular model label
     */
    public static function getModelLabel(): string
    {
        return __('filament-panels.question_request.singular');
    }

    /**
     * Get the plural model label for the page
     *
     * @return string The localized plural model label
     */
    public static function getPluralModelLabel(): string
    {
        return __('filament-panels.question_request.plural');
    }

    /**
     * Get the navigation label for the sidebar
     *
     * @return string The localized navigation label
     */
    public static function getNavigationLabel(): string
    {
        return __('filament-panels.question_request.title');
    }
    /**
     * Prepare and return data for the view
     *
     * Retrieves questions that:
     * - Don't have a tutor assigned yet
     * - Match the current tutor's subjects and grades
     *
     * This method uses the same query logic as getNavigationBadge() but
     * returns the full question records instead of just a count.
     *
     * @return array Array containing questions that match the tutor's expertise
     */
    public function getViewData(): array
    {
        // Get the current tutor's ID from the authenticated user
        $tutorId = Tutor::where('user_id', Auth::id())->value('id');

        // Get the tutor's subjects and grades they can teach
        $tutorSubjects = TutorSubject::where('tutor_id', $tutorId)
            ->get(['grade_id', 'subject_id']);

        // Get questions that:
        // 1. Don't have a tutor assigned yet 
        // 2. Match the tutor's subjects and grades
        $questions = Question::whereNull('tutor_id')
            ->where(function ($query) use ($tutorSubjects) {
                foreach ($tutorSubjects as $subject) {
                    $query->orWhere(function ($q) use ($subject) {
                        $q->where('grade_id', $subject->grade_id)
                            ->where('subject_id', $subject->subject_id);
                    });
                }
            })
            ->latest()
            ->get();

        return [
            'questions' => $questions,
        ];
    }

    /**
     * Accept a question for answering
     *
     * This method:
     * - Checks if the question is still available 
     * - Assigns the current tutor to the question
     * - Shows a success notification
     * - Redirects to the answer page for the accepted question
     *
     * @param int $questionId The ID of the question to accept
     * @return mixed Redirect response or void if question already accepted
     */
    public function acceptQuestion(int $questionId)
    {
        // Find the question by ID or fail with a 404
        $question = Question::findOrFail($questionId);

        // Check if the question has already been accepted by another tutor
        if ($question->tutor_id !== null) {
            // Show error notification if question is already taken
            Notification::make()
                ->title('This question was already accepted by another tutor.')
                ->danger()
                ->send();
            return;
        }

        // Get the current tutor's ID from the authenticated user
        $tutorId = Tutor::where('user_id', Auth::id())->value('id');

        // Assign the tutor to the question
        $question->update(['tutor_id' => $tutorId]);

        // Show success notification
        Notification::make()
            ->title(__('filament-panels.question.question_accepted'))
            ->success()
            ->send();

        // Redirect to the answer page for this question
        return redirect('/tutor/answer-question/' . $question->id);
    }
}
