
<?php if (isset($component)) { $__componentOriginalbe23554f7bded3778895289146189db7 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbe23554f7bded3778895289146189db7 = $attributes; } ?>
<?php $component = Filament\View\LegacyComponents\Page::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Filament\View\LegacyComponents\Page::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="grid gap-4 max-w-xl">
        <h1 class="text-2xl font-bold">توليد تقرير </h1>

        
        <div>
            
            <label class="block mb-1 text-sm">نوع التقرير</label>

            
            <select wire:model.defer="reportType" class="w-full border rounded p-2">
                <option value="tutors">تقرير المعلمين</option>
                <option value="revenue">تقرير الإيرادات</option>
                <option value="subscriptions">تقرير الاشتراكات</option>
            </select>
        </div>

        
        <div>
            
            <label class="block mb-1 text-sm">الفترة الزمنية</label>

            
            <select wire:change="updateRange($event.target.value)" class="w-full border rounded p-2">
                <option value="week">الأسبوع</option>
                <option value="month">الشهر</option>
                <option value="year">السنة</option>
            </select>
        </div>


        
        <!--[if BLOCK]><![endif]--><?php if($range === 'month'): ?>
        <div>
            <label class="block mb-1 text-sm">اختر الشهر</label>

            <select wire:model.defer="selectedMonth" class="w-full border rounded p-2">
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $availableMonths; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $number => $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option value="<?php echo e($number); ?>"><?php echo e($month); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
            </select>
        </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        
        <div>
            <?php if (isset($component)) { $__componentOriginal6330f08526bbb3ce2a0da37da512a11f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.button.index','data' => ['wire:click' => 'generateReport','color' => 'primary']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wire:click' => 'generateReport','color' => 'primary']); ?>
                توليد التقرير
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $attributes = $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $component = $__componentOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
        </div>
    </div>

    
    <!--[if BLOCK]><![endif]--><?php if($pdfUrl): ?>
    <div class="mt-10">
        <h2 class="text-lg font-semibold mb-2">معاينة التقرير</h2>

        <iframe src="<?php echo e($pdfUrl); ?>" width="100%" height="600" class="rounded border shadow-md"></iframe>

        <div class="mt-4">
            <a href="<?php echo e($pdfUrl); ?>" target="_blank"
                class="inline-flex items-center px-4 py-2 bg-danger-600 text-white rounded hover:bg-danger-700">
                تحميل PDF
            </a>
        </div>
    </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbe23554f7bded3778895289146189db7)): ?>
<?php $attributes = $__attributesOriginalbe23554f7bded3778895289146189db7; ?>
<?php unset($__attributesOriginalbe23554f7bded3778895289146189db7); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbe23554f7bded3778895289146189db7)): ?>
<?php $component = $__componentOriginalbe23554f7bded3778895289146189db7; ?>
<?php unset($__componentOriginalbe23554f7bded3778895289146189db7); ?>
<?php endif; ?><?php /**PATH C:\xampp\htdocs\work\addrus-pay\resources\views/filament/admin/pages/reports.blade.php ENDPATH**/ ?>