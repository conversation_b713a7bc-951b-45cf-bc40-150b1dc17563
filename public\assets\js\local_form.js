document.addEventListener('DOMContentLoaded', function () {
    let resendTimer;

    const csrf = document.querySelector('meta[name="csrf-token"]').getAttribute("content");
    const otpCard = document.getElementById("otpCard");
    const otpButton = document.getElementById('otp-button');
    const otpButtonText = document.getElementById('otp-button-text');
    const resendButton = document.getElementById('resend-otp-button');
    const resendButtonText = document.getElementById('resend-otp-button-text');
    const otpButtonSpinner = document.getElementById('otp-button-spinner');

    const email = document.getElementById('email');
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const phone = document.getElementById('phone');
    const phonePattern = /^09\d{8}$/;
    const otp = document.getElementById('otp');
    const otpPattern = /^[0-9]{6}$/;

    // This will start a countdown timer for the resend button
    // It will disable the resend button and update the text to show the countdown
    // When the countdown reaches 0, it will enable the resend button and reset the text
    function startResendCountdown() {
        if (resendTimer) {
            clearInterval(resendTimer);
        }
        let countdown = 30;
        resendButton.disabled = true;
        resendButtonText.innerText = `إعادة الإرسال (${countdown})`;
        resendTimer = setInterval(() => {
            countdown--;
            if (countdown <= 0) {
                clearInterval(resendTimer);
                resendButton.disabled = false;
                resendButtonText.innerText = 'إعادة الإرسال';
            } else {
                resendButtonText.innerText = `إعادة الإرسال (${countdown})`;
            }
        }, 1000);
    }

    // This will send an OTP to the user's phone number when clicked
    // It will also display the OTP card and scroll to it
    // It will also start a countdown timer for the resend button
    // If there is an error, it will display an error message to the user
    document.querySelectorAll('button[type="button"]').forEach((button) => {
        button.addEventListener('click', async () => {
            const radioSelected = document.querySelector('input[name="plan_id"]:checked');

            const errorMessages = document.getElementById('detailsValidation');
            errorMessages.innerHTML = '';

            let isValid = true;

            const errorDiv = document.createElement('div');
            errorDiv.className = 'alert alert-danger m-0 mt-5';

            otpButtonText.classList.add('d-none');
            otpButtonSpinner.classList.remove('d-none');

            // Check if the radio button is selected
            if (!radioSelected) {
                const error = document.createElement('li');
                error.innerText = 'يرجى اختيار خطة اشتراك.';
                errorDiv.appendChild(error);
                isValid = false;
            }
            // Check if the email is valid
            if (!email.value.trim() == '') {
                if (!emailPattern.test(email.value)) {
                    const error = document.createElement('li');
                    error.innerText = 'يرجى إدخال عنوان بريد إلكتروني بالشكل التالي <EMAIL>.';
                    errorDiv.appendChild(error);
                    isValid = false;
                }
            }
            // Check if the phone is valid
            if (!phonePattern.test(phone.value)) {
                const error = document.createElement('li');
                error.innerText = 'يرجى إدخال رقم هاتف صحيح بالشكل التالي 0912345678.';
                errorDiv.appendChild(error);
                isValid = false;
            }
            // If any of the fields are invalid, display an error message to the user
            if (!isValid) {
                otpButtonText.classList.remove('d-none');
                otpButtonSpinner.classList.add('d-none');
                errorMessages.appendChild(errorDiv);
            } else {
                // Send OTP to the user's phone number
                try {
                    const response = await fetch(`https://pay.addrus.com/verify-user/${encodeURIComponent(phone.value)}`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': csrf
                        }
                    });
                    otpButton.classList.add('d-none');
                    otpCard.classList.remove('d-none');
                    startResendCountdown();
                } catch (e) {
                    // If there is an error, display an error message to the user
                    const error = document.createElement('li');
                    error.innerText = 'حدث خطأ أثناء إرسال رمز التحقق. يرجى المحاولة مرة أخرى.';
                    errorDiv.appendChild(error);
                    errorMessages.appendChild(errorDiv);
                } finally {
                    // Finally, re-enable the submit button and hide the loading spinner
                    otpButtonText.classList.remove('d-none');
                    otpButtonSpinner.classList.add('d-none');
                }
            }
        });
    });

    // This will listen for the phone input field to change
    // If the phone input field changes, it will hide the OTP card and show the OTP button
    otpButton.addEventListener('click', async () => {
        phone.addEventListener('input', () => {
            otpCard.classList.add('d-none');
            otpButton.classList.remove('d-none');

            clearInterval(resendTimer);
            resendButton.disabled = false;
            resendButtonText.innerText = 'إعادة الإرسال';
        });
    });

    // Validation method for the subscription form
    // This method will validate the email, phone, and selected card radio button
    // If any of the fields are invalid, it will prevent the form from submitting
    // and display an error message to the user
    document.getElementById('subscriptionForm').addEventListener('submit', async function (event) {
        event.preventDefault();

        const radioSelected = document.querySelector('input[name="plan_id"]:checked');

        var submitButton = document.getElementById('submit-button');
        var buttonText = document.getElementById('button-text');
        var buttonSpinner = document.getElementById('button-spinner');

        const errorMessages = document.getElementById('otpValidation');
        errorMessages.innerHTML = '';

        let isValid = true;

        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-danger m-0 mt-5';

        // Check if the radio button is selected
        if (!radioSelected) {
            const error = document.createElement('li');
            error.innerText = 'يرجى اختيار خطة اشتراك.';
            errorDiv.appendChild(error);
            isValid = false;
        }
        // Check if the email is valid
        if (!email.value.trim() == '') {
            if (!emailPattern.test(email.value)) {
                const error = document.createElement('li');
                error.innerText = 'يرجى إدخال عنوان بريد إلكتروني بالشكل التالي <EMAIL>.';
                errorDiv.appendChild(error);
                isValid = false;
            }
        }
        // Check if the phone is valid
        if (!phonePattern.test(phone.value)) {
            const error = document.createElement('li');
            error.innerText = 'يرجى إدخال رقم هاتف صحيح بالشكل التالي 1234567890+.';
            errorDiv.appendChild(error);
            isValid = false;
        }
        // Check if the OTP is valid
        if (!otpPattern.test(otp.value)) {
            const error = document.createElement('li');
            error.innerText = 'يرجى إدخال رمز التحقق المكون من 6 أرقام.';
            errorDiv.appendChild(error);
            isValid = false;
        }
        // If any of the fields are invalid, display an error message to the user
        if (!isValid) {
            errorMessages.appendChild(errorDiv);
        } else {
            // Show loading spinner and disable submit button
            buttonText.classList.add('d-none');
            buttonSpinner.classList.remove('d-none');
            submitButton.disabled = true;
            // Send OTP to the server for verification
            try {
                const otp = document.getElementById('otp');
                const response = await fetch(`https://pay.addrus.com/verify-otp/${encodeURIComponent(phone.value)}/${encodeURIComponent(otp.value)}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrf
                    }
                });
                if (response.ok) {
                    // If the OTP is correct, submit the form
                    this.submit();
                    otpButton.classList.add('d-none');
                    otpCard.classList.remove('d-none');
                    startResendCountdown();
                } else {
                    // Otherwise, display an error message to the user
                    // And re-enable the submit button and hide the loading spinner
                    const error = document.createElement('li');
                    error.innerText = 'رمز التحقق غير صحيح. يرجى المحاولة مرة أخرى.';
                    errorDiv.appendChild(error);
                    errorMessages.appendChild(errorDiv);
                    otp.text = '';
                    submitButton.disabled = false;
                    buttonText.classList.remove('d-none');
                    buttonSpinner.classList.add('d-none');
                }
            } catch (e) {
                // If there is an error, display an error message to the user
                const error = document.createElement('li');
                error.innerText = 'حدث خطأ أثناء إرسال رمز التحقق. يرجى المحاولة مرة أخرى.';
                errorDiv.appendChild(error);
                errorMessages.appendChild(errorDiv);
            }
        }
    });
});
