<?php

namespace App\Http\Controllers;

use App\Enums\Currency;
use App\Enums\PaymentProvider;
use App\Enums\SubscriptionPlanType;
use App\Enums\TransactionStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\InitiatePaymentRequest;
use App\Mail\sendSubscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use App\Models\Subscription;
use App\Models\SubscriptionPlan;
use App\Http\Requests\Api\TransactionRequest;
use App\Models\Transaction;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Jobs\CheckTransactionStatus;
use App\Jobs\SendEmail;
use App\Jobs\SendText;
use App\Jobs\CheckUsersAndCreate;
use App\Jobs\SendSms;

/*
This class is responsiple for:
    *calling T-lync API.
    *Recieving transaction receipt and save it to the database.
    *Sending the necessery data to the front-end.
*/

class InitiatePaymentController extends Controller
{

    // This function searchs for valid subscription
    public function findSubscription($planId){

        // Getting subscription from subscripions table
        $subscription = Subscription::where('subscription_plan_id', $planId)
                                        ->where('status', 0)
                                        ->select('id', 'email', 'password')
                                        ->first();

        // Check if subscription exists
        if($subscription){

            // Return the found subscription
            return $subscription;

        }else{
            
            return null;
        }
    }

    // This function Initiates the Payment with the user credintials
    public function initiate(InitiatePaymentRequest $request){

        try{

            $validatedRequest = $request->validated();
            Log::info($validatedRequest);

            // Assign form values after validation
            $amount = SubscriptionPlan::where('id', $validatedRequest['plan_id'])->value('price');
            $userEmail = $validatedRequest['email'];
            $userPhone = '+218' . substr($validatedRequest['phone'], 1);
            $chosenPlanId = $validatedRequest['plan_id'];

            // Create custom 'random' reference for every user
            $customRef = Str::uuid()->toString();

            // Manually validate the custom reference
            $validator = Validator::make(
            ['custom_ref' => $customRef],
            ['custom_ref' => 'required|string|unique:transactions,uuid']
            );

            // Check if the customer reference did not pass!
            if ($validator->fails()) {
                throw new ValidationException($validator);
            }

            // Data payload for the Tlync API
            $data  = [
                'id' => config('api.store_id'),
                'amount' => $amount,
                'phone' => $userPhone,
                'backend_url' => config('api.backend_url'),
                'custom_ref' => $customRef,
                'frontend_url' => config('api.frontend_url'),
                'failed_front_end_url' => config('api.failed_frontend_url'),
            ];
            // Add email to the array only if it is not empty 'null'
            if ($userEmail !== null) {

                $data['email'] = $userEmail;

            }
            
            Log::info('PayLoad', [$data]);

            try {
                // Call T-lync API
                $response = Http::timeout(60)->withHeaders(['Accept' => 'application/json'])
                ->withToken(config('api.api_token'))->post(config('api.payment_api_live'), $data);

            } catch (\Exception $error) {

                // In case T-lync API call failed
                Log::error($error);
                return $this->showErrorPage('تعذر إتمام العملية', 'يرجى التحقق من المعلومات المدخلة والمحاولة مرة أخرى. إذا استمرت المشكلة، يرجى التواصل مع الدعم الفني.');
            }

            // Check if the pay initiating was successful
            if($response->successful()){

                $responseData = $response->json();

                try{
                    // Start transaction.
                    DB::transaction(function () use($responseData, $chosenPlanId, $data) {

                        // Get a subscription
                        $subscription = $this->findSubscription($chosenPlanId);

                        // Checking and creating accounts
                        CheckUsersAndCreate::dispatch();

                        // In case there were no subscriptions 'worst case scinario'.
                        if ($subscription === null) {

                           throw new \Exception('Somthing wrong with finding subscription');

                        }else{

                            // Change the status to taken
                            $subscription->status = 1;
                            $subscription->save();
                        }

                        // Save the transaction details to the database
                        $transaction=  Transaction::Create(
                            [
                                'email' => $data['email'] ?? '',
                                'phone' => $data['phone'],
                                'subscription_id' => $subscription->id,
                                'payment_provider' => PaymentProvider::TLYNC,
                                'amount' => $data['amount'],
                                'currency' => Currency::LYD,
                                'status' => TransactionStatus::PENDING,
                                'uuid' => $data['custom_ref'],

                            ]);

                        // Loading the subscription to be accessable in the Check transaction Job
                        $transaction->load('subscription');

                        if(!isset($responseData['url'])){

                            Log::info($responseData);

                        }
                        // Call a Job to check of the transaction was successful and send subscription
                        CheckTransactionStatus::dispatch($transaction)->delay(now()->addMinutes(1));

                    });
                    // Saves the custom reference to the session to retrieve it in the success and fail pages
                    session(['customRef' => $data['custom_ref'] ]);
                    
                    // Re-direct to the purchase page of T-lync
                    return redirect()->away($responseData['url']);

                }catch(\Exception $error) {

                    // An error occured through the transaction
                    Log::error("Error in DB transaction: ", [$error]);
                    return $this->showErrorPage('تعذر إتمام العملية', 'يرجى التحقق من المعلومات المدخلة والمحاولة مرة أخرى. إذا استمرت المشكلة، يرجى التواصل مع الدعم الفني.');
                }

            }else{
                Log::info($response->json());
                Log::error('Payment API error: ', [$response->body()]);
            }

        }catch(\Exception $error){

            Log::error("error!: ", [$error->getMessage()]);
            return $this->showErrorPage('تعذر إتمام العملية', 'يرجى التحقق من المعلومات المدخلة والمحاولة مرة أخرى. إذا استمرت المشكلة، يرجى التواصل مع الدعم الفني.');
        }
    }

    // This function recieves transaction receipt from Tlync
    public function transaction(TransactionRequest $request) {

        Log::info('Call back Request -> ', [$request]);

        try{
            // Getting transaction data form T-lync after successful transaction
            $data = $request->validated();
            $status = $data['result'];

            $transaction = Transaction::where('uuid', $data['custom_ref'])
            ->with('subscription')
            ->first();

            $subscription = $transaction->subscription;

            if($status === 'success'){
                
                if($transaction->email !== ''){

                    // Send subscription via email if email exists
                    SendEmail::dispatch($transaction);
                }
                
                // Send subscription via whatsApp
                $accountData=[];
                $accountData[] = $transaction->subscription->email;
                $accountData[] = $transaction->subscription->password;
                SendText::dispatch($transaction->phone, 'sendAccountData',$accountData);
                
                // Update the status to completed and set the external reference to T-lync reference
                Transaction::where('uuid', $data['custom_ref'])->update([
                    'status' => TransactionStatus::COMPLETED,
                    'external_ref' => $data['our_ref'],
                ]);

            }else{

                // Update the status to failed
                Transaction::where('uuid', $data['custom_ref'])->update(['status' => TransactionStatus::FAILED]);

                // change the status to not taken
                $subscription->status = 0;
                $subscription->save();
            }

        }catch(\Exception $error){

            // log in case of incomplete transaction
            Log::error("error!: ", [$error->getMessage()]);

            return $this->showErrorPage('تعذر إتمام العملية', 'يرجى التحقق من المعلومات المدخلة والمحاولة مرة أخرى. إذا استمرت المشكلة، يرجى التواصل مع الدعم الفني.');
        }
    }

    // This function redirects to the failed payment page
    public function showErrorPage($error, $message)
    {
        return view('local.error', [
            'error' => $error,
            'message' => $message,
        ]);
    }

    // This function show success page with the subscription after successful transaction
    public function showPaymentSucceeded(){

        $customRef = session('customRef');
        session()->forget('customRef');

        $transaction = Transaction::with('subscription')
        ->where('uuid', $customRef)
        ->first();

        Log::info('in success function -> ', [$customRef, $transaction, $transaction->subscription]);

        return view('local.reciept', [
            'email' => $transaction->subscription->email,
            'password' => $transaction->subscription->password,
        ]);
    }

    // This function setting the status in case of a failed payment
    public function showPaymentFailed()
    {
        $customRef = session('customRef');
        session()->forget('customRef');

        // Null check for the custom reference
        if (!$customRef) {
            return view('local.error', [
                'error' => 'إنتهت عملية الدفع',
                'message' => 'يمكن الخروج أو العودة إلى الصفحة الرئيسية.',
                ]);
            }

        $transaction = Transaction::with('subscription')
        ->where('uuid', $customRef)
        ->first();

        // Check if the transaction is already completed
        if($transaction->status === TransactionStatus::COMPLETED){
            return view('local.error', [
                'error' => 'إنتهت عملية الدفع',
                'message' => 'يمكن الخروج أو العودة إلى الصفحة الرئيسية.',
            ]);
        }

        // Setting the transaction to failed
        $transaction->status = TransactionStatus::FAILED;
        $transaction->save();

        // Setting subscription status back to not taken
        $transaction->subscription->status = 0;
        $transaction->subscription->save();
        
        return view('local.error', [
            'error' => 'تم إلغاء العملية',
            'message' => 'حدث خطأ أثناء عملية الدفع, يرجى التأكد من البيانات والمحاولة مرة أخرى.',
        ]);
    }
}