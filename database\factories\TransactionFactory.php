<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\Subscription;
use App\Models\Transaction;

class TransactionFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Transaction::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'email' => fake()->safeEmail(),
            'phone' => fake()->phoneNumber(),
            'subscription_id' => Subscription::factory(),
            'payment_provider' => fake()->randomElement(["TLYNC","STRIPE"]),
            'amount' => fake()->randomFloat(2, 0, 99999999.99),
            'currency' => fake()->randomElement(["USD","EUR","LYD"]),
            'status' => fake()->randomElement(["PENDING","COMPLETED","FAILED"]),
            'created_at' => fake()->dateTime(),
            'updated_at' => fake()->dateTime(),
            'uuid' => fake()->uuid(),
        ];
    }
}
