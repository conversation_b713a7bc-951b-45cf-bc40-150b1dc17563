<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SendWhatsAppPayment implements ShouldQueue
{
    use Queueable;

    public $payload;
    public $type;


    public function __construct( $payload,$type)
    {
        $this->payload = $payload;
        $this->type=$type;
    }

     /**
     * The job to send a WhatsApp interactive message or individual.
     * 
     * Interactive messages allow users to respond by clicking on:
     * - Buttons (Quick reply options)
     * - Lists (Dropdown-style menu for multiple choices)
     * 
     * This function makes a POST request to the WhatsApp API with the provided payload.
     */

    public function handle(): void
    {
        // Construct API endpoint using the configured WhatsApp API URL and phone number ID
        $url = config('api.whatsapp_api_url') . ($this->type==='AskMe'? config('api.whatsapp_askMe_phone_id'):  config('api.whatsapp_phone_id')). "/messages";
        // Send the request to WhatsApp API using the provided access token
        Log::info("usrl ".$url);
        $response = Http::withToken(config('api.whatsapp_access_token'))
            ->post($url, $this->payload);
        Log::info("WhatsApp Message Sent: " . json_encode($response->json()));
    }
}
