created:
    - database/factories/SubscriptionPlanFactory.php
    - database/factories/SubscriptionFactory.php
    - database/factories/TransactionFactory.php
    - database/factories/PaymentCallbackFactory.php
    - database/migrations/2025_02_15_121635_create_subscription_plans_table.php
    - database/migrations/2025_02_15_121636_create_subscriptions_table.php
    - database/migrations/2025_02_15_121637_create_transactions_table.php
    - database/migrations/2025_02_15_121638_create_payment_callbacks_table.php
    - app/Models/SubscriptionPlan.php
    - app/Models/Subscription.php
    - app/Models/Transaction.php
    - app/Models/PaymentCallback.php
    - database/seeders/SubscriptionPlanSeeder.php
    - database/seeders/SubscriptionSeeder.php
    - database/seeders/TransactionSeeder.php
    - database/seeders/PaymentCallbackSeeder.php
models:
    User: { name: string, email: string, email_verified_at: 'timestamp nullable', password: string, remember_token: 'string:100 nullable' }
    SubscriptionPlan: { type: 'enum:LOCAL,INTERNATIONAL', period: integer, price: 'decimal:10,2', created_at: timestamp, updated_at: timestamp, expiration_date: timestamp, relationships: { hasMany: Subscription } }
    Subscription: { email: 'string:255', password: 'string:100', status: boolean, subscription_plan_id: 'id foreign:subscription_plans', created_at: timestamp, updated_at: timestamp, relationships: { belongsTo: SubscriptionPlan, hasMany: Transaction } }
    Transaction: { email: 'string:255', phone: 'string:20', subscription_id: 'id foreign:subscriptions', payment_provider: 'enum:TLYNC,STRIPE', amount: 'decimal:10,2', currency: 'enum:USD,EUR,LYD', status: 'enum:PENDING,COMPLETED,FAILED default:PENDING', created_at: timestamp, updated_at: timestamp, uuid: 'uuid unique', relationships: { belongsTo: Subscription, hasMany: PaymentCallback } }
    PaymentCallback: { transaction_id: 'id foreign:transactions', response: 'json nullable', callback_time: timestamp, created_at: timestamp, updated_at: timestamp, relationships: { belongsTo: Transaction } }
