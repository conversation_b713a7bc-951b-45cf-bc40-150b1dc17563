<?php

namespace App\Models\AskMe;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TutorActivity extends Model
{
    protected $connection = 'mysql_secondary';

    protected $fillable = [
        'action',
        'question_id',
        'tutor_id',
        'note'
    ];
    protected $casts = [
        'id' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];


    public function question(): BelongsTo 
    {
        return $this->belongsTo(Question::class);
    }
    public function tutor(): BelongsTo 
    {
        return $this->belongsTo(Tutor::class);
    }
}
