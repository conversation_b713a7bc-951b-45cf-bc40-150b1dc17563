<?php
namespace App\Filament\Admin\Widgets;

use App\Enums\TransactionStatus;
use App\Models\AskMe\Transaction;
use Filament\Widgets\ChartWidget;

/**
 * Revenue Comparison Chart Widget
 *
 * Displays a line chart comparing revenue data between the current month and previous month.

 */
class RevenueComparisonChart extends ChartWidget
{
    protected static ?string $heading = 'مقارنة الإيرادات (هذا الشهر مقابل الشهر الماضي)';
    protected static ?int $sort = 3;
    protected int | string | array $columnSpan = 'full';
    protected static ?string $maxHeight = '300px';

    /**
     * Generate the data for the revenue comparison chart
     *
     * This method calculates daily revenue data for both the current month and
     * the previous month, allowing for a direct day-by-day comparison.
     *
     * @return array The chart data configuration
     */
    protected function getData(): array
    {
        // Get the first day of the current month and previous month
        $thisMonth = now()->startOfMonth();
        $lastMonth = now()->subMonth()->startOfMonth();

        // Initialize collections to store the revenue data
        $thisMonthData = collect();
        $lastMonthData = collect();
        $labels = [];

        // Loop through each day of the month 
        for ($day = 1; $day <= 31; $day++) {
            // Create date objects for the current day in both months
            $thisDate = $thisMonth->copy()->setDay($day);
            $lastDate = $lastMonth->copy()->setDay($day);

            // Stop loop if day exceeds actual days in either month
            if (!$thisDate->isSameMonth($thisMonth) || !$lastDate->isSameMonth($lastMonth)) {
                break;
            }

            // Add the day number to the labels array
            $labels[] = (string) $day;

            // Calculate revenue for this day in the current month
            $thisRevenue = Transaction::where('status', TransactionStatus::COMPLETED)
                ->whereDate('created_at', $thisDate)
                ->sum('amount');

            // Calculate revenue for this day in the previous month
            $lastRevenue = Transaction::where('status', TransactionStatus::COMPLETED)
                ->whereDate('created_at', $lastDate)
                ->sum('amount');

            // Add the calculated revenues to their respective collections
            $thisMonthData->push(round($thisRevenue, 2));
            $lastMonthData->push(round($lastRevenue, 2));
        }

        return [
            'datasets' => [
                [
                    // Current month dataset (blue)
                    'label' => __('filament-panels.dashboard.this_month'),
                    'data' => $thisMonthData,
                    'borderColor' => '#3b82f6', // Blue line color
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)', // Light blue fill
                    'fill' => true, // Fill area under the line
                    'tension' => 0.4, 
                ],
                [
                    // Previous month dataset (orange)
                    'label' => __('filament-panels.dashboard.last_month'),
                    'data' => $lastMonthData,
                    'borderColor' => '#f97316', // Orange line color
                    'backgroundColor' => 'rgba(249, 115, 22, 0.1)', // Light orange fill
                    'fill' => true, // Fill area under the line
                    'tension' => 0.4, 
                ],
            ],
            'labels' => $labels,
        ];
    }


    protected function getType(): string
    {
        return 'line';
    }
}
