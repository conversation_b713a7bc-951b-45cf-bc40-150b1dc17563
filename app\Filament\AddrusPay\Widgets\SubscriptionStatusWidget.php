<?php

namespace App\Filament\AddrusPay\Widgets;

use App\Models\Subscription;
use Filament\Support\Colors\Color;
use Filament\Widgets\ChartWidget;

class SubscriptionStatusWidget extends ChartWidget
{
    protected static ?int $sort = 4;

    protected static ?string $heading = 'حالة الاشتراكات';

    protected function getData(): array
    {
        // Get counts by subscription status
        $usedCount = Subscription::where('status', 1)->count();
        $unusedCount = Subscription::where('status', 0)->count();

        return [
            'datasets' => [
                [
                    'label' => 'حالة الاشتراكات',
                    'data' => [
                        $usedCount,
                        $unusedCount,
                    ],
                    'backgroundColor' => [
                        'rgb(76, 175, 80)',  // Used subscriptions - Material Green 500
                        'rgb(255, 193, 7)',  // Unused subscriptions - Material Amber 500
                    ],
                ],
            ],
            'labels' => [
                'مستخدم',
                'غير مستخدم',
            ],
        ];
    }

    protected function getType(): string
    {
        return 'doughnut';
    }

    protected function getOptions(): array
    {
        return [
            'scales' => [
                'y' => [
                    'display' => false,
                ],
                'x' => [
                    'display' => false,
                ],
            ],
        ];
    }
}