<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ProductsSeeder extends Seeder
{
    public function run()
    {
        DB::table('products')->insert([

            [
                'name' => 'Casio',
                'price' => 130.00,
                "code" => "gz359z7in6",
                'quantity' => 10,
                'categories_id' => 1,
                'created_at' => Carbon::now(),
            ],
            [
                'name' => 'EDIFICE',
                'price' => 820.00,
                "code" => "yb2m46clim",
                'quantity' => 5,
                'catalog_id' => 1,
                'created_at' => Carbon::now(),
            ],
            [
                'name' => 'pro trek',
                'price' => 820.00,
                "code" => "svknt0n8wc",

                'quantity' => 10,
                'catalog_id' => 1,
                'created_at' => Carbon::now(),
            ],
            [
                'name' => 'Casio',
                'price' => 250.00,
                "code" => "a5hpzu5012",
                'quantity' => 10,
                'catalog_id' => 2,
                'created_at' => Carbon::now(),
            ],
            [
                'name' => 'Casio',
                'price' => 180.00,
                "code" => "ifrs05g7cj",

                'quantity' => 10,
                'catalog_id' => 2,
                'created_at' => Carbon::now(),
            ],
            [
                'name' => 'Casio',
                'price' => 260.00,
                "code" => "txotqhb0zb",
                'quantity' => 10,
                'catalog_id' => 2,
                'created_at' => Carbon::now(),
            ],

        ]);
    }
}
