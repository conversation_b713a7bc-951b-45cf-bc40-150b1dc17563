<?php

namespace App\Filament\Tutor\Pages;

use App\Enums\TutorActivities;
use App\Models\AskMe\Question;
use App\Models\AskMe\Tutor;
use App\Models\AskMe\TutorActivity;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Auth;

/**
 * ViewAndAcceptQuestion Page
 *
 * This page displays a single question in detail and allows a tutor to accept it.
 */
class ViewAndAcceptQuestion extends Page
{
  
    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static string $view = 'filament.pages.view-and-accept-question';
    protected static bool $shouldRegisterNavigation = false;
    public ?Question $question = null;
    /**
     * Get the title for the page
     *
     * @return string The localized page title
     */
    public function getTitle(): string
    {
        return __('filament-panels.question_request.plural');
    }

    /**
     * Get the singular model label for the page
     *
     * @return string The localized singular model label
     */
    public static function getModelLabel(): string
    {
        return __('filament-panels.question_request.singular');
    }

    /**
     * Get the plural model label for the page
     *
     * @return string The localized plural model label
     */
    public static function getPluralModelLabel(): string
    {
        return __('filament-panels.question_request.plural');
    }

    /**
     * Get the navigation label for the sidebar
     * @return string The localized navigation label
     */
    public static function getNavigationLabel(): string
    {
        return __('filament-panels.question_request.title');
    }
    /**
     * Define the dynamic route pattern for this page
     * @return string The route pattern
     */
    public static function getSlug(): string
    {
        return 'view-and-accept-question/{record}';
    }

    /**
     * Initialize the page with the question data
     *
     * @param Question $record The question being viewed
     * @return void
     */
    public function mount(Question $record): void
    {
        $this->question = $record;
    }

    /**
     * Accept the current question for answering
     *
     * This method:
     * - Checks if the question is still available (not accepted by another tutor)
     * - Assigns the current tutor to the question
     * - Records the acceptance activity in the tutor activity log
     *
     * @return void
     */
    public function accept(): void
    {
        // Check if the question has already been accepted by another tutor
        if ($this->question->tutor_id !== null) {
            // Show error notification if question is already taken
            Notification::make()
                ->title('This question has already been accepted.')
                ->danger()
                ->send();
            return;
        }

        // Get the current tutor's ID from the authenticated user
        $tutorId = Tutor::where('user_id', Auth::id())->value('id');

        // Assign the tutor to the question
        $this->question->update(['tutor_id' => $tutorId]);

        // Record the acceptance activity in the tutor activity log
        TutorActivity::create([
            'tutor_id' => $tutorId,
            'question_id' => $this->question->id,
            'activity_type' => TutorActivities::Accept,
        ]);

        // Show success notification
        Notification::make()
            ->title(__('filament-panels.question.notification.question_accepted'))
            ->success()
            ->send();

        // Redirect to the answer page for this question
        $this->redirect('/tutor/answer-question/' . $this->question->id);
    }
}
