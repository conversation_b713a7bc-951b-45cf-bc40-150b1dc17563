<?php

namespace App\Filament\Admin\Widgets;

use App\Models\AskMe\SubscriptionPackage;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;

/**
 * Package Performance Table Widget
 *
 * Displays a table of subscription package performance metrics.
 */
class PackagePerformanceTable extends BaseWidget
{

    protected static ?string $heading = 'تحليل أداء الباقات';
    protected static ?int $sort = 5;
    protected int | string | array $columnSpan = 'full';

    /**
     * Define the table structure for displaying package performance metrics
     *
     * @param Table $table The Filament table instance
     * @return Table The configured table
     */
    public function table(Table $table): Table
    {
        return $table
            ->query(
                // Query all subscription packages and count their subscriptions
                SubscriptionPackage::query()->withCount('subscriptions')
            )
            ->columns([
                // Package name column
                TextColumn::make('name')
                    ->label(__('filament-panels.dashboard.package_name'))
                    ->sortable(),

                // Package price column with currency formatting
                TextColumn::make('price')
                    ->label(__('filament-panels.dashboard.package_price'))
                    ->formatStateUsing(fn ($state) => number_format($state, 2) . ' د.ل'), // Format with Libyan Dinar

                // Number of subscriptions sold column
                TextColumn::make('subscriptions_count')
                    ->label(__('filament-panels.dashboard.package_sold'))
                    ->badge()
                    ->color('primary'),

                // Total revenue column (calculated as price × number of subscriptions)
                TextColumn::make('total_revenue')
                    ->label(__('filament-panels.dashboard.total_revenue'))
                    ->state(fn ($record) => $record->price * $record->subscriptions_count)
                    ->formatStateUsing(fn ($state) => number_format($state, 2) . ' د.ل')
                    ->badge()
                    ->color('success'),
            ]);
    }
}
