<?php

namespace App\Enums;

enum TransactionStatus: string
{
    case PENDING = 'PENDING';
    case PAID= 'PAID';
    case COMPLETED = 'COMPLETED';
    case FAILED = 'FAILED';
    case CANCELED='CANCELED';

    public function label(): string
    {
        return match($this) {
            self::PENDING => 'قيد الانتظار',
            self::PAID => 'مدفوعة',
            self::COMPLETED => 'مكتمل',
            self::FAILED => 'فشل',
            self::CANCELED => 'تم الالغاء',
            default => 'Unknown',

        };
    }

    public function color(): string
    {
        return match($this) {
            self::PENDING => 'yellow',
            self::COMPLETED => 'green',
            self::FAILED => 'red',
            self::CANCELED => 'blue',
            default => 'gray',

        };
    }
}
