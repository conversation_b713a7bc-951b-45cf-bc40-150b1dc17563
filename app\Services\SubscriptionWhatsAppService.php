<?php

namespace App\Services;

use App\Enums\SubscriptionPlanType;
use App\Models\SubscriptionPlan;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Enums\TransactionStatus;
use App\Http\Controllers\StripePaymentController;
use App\Jobs\WhatsAppTlync;
use App\Services\WhatsAppService;

class SubscriptionWhatsAppService
{

    protected $whatsapp;

    public function __construct()
    {
        $this->whatsapp = new WhatsAppService();
    }

    /**
     * Handles WhatsApp webhook events for addrus pay subscription interactions.
     *
     * 1- If the webhook contains a message status update, handle it.
     * 2- If it is a new message not interactive, restart the flow with a welcome message.
     * 3- If the user selected an option from a list, handle their selection .
     *
     * @param array $value  the payload value from WhatsApp webhook
     */

    public function SubscriptionWhatsAppWebhook($value)
    {
        Log::info("message comes");
        if (isset($value['messages']) && is_array($value['messages']) && !empty($value['messages'])) {
            // Restart the flow if the user sends a new message (not an interactive selection)
            if (!isset($value['messages'][0]['interactive'])) {
                $message = $value['messages'][0];
                return $this->sendLocalWelcomeMessage($message['from']);
            }
        }
        // Check if the message is an interactive list selection
        if (isset($value['messages'][0]['interactive']['list_reply']['id'])) {
            $selection = $value['messages'][0]['interactive']['list_reply']['id'] ?? null;
            $from = $value['messages'][0]['from'];

            // Handle interactive list selections from the user
            match (true) {
                $selection === SubscriptionPlanType::INTERNATIONAL->value => $this->sendSubscriptionPlans($from, SubscriptionPlanType::INTERNATIONAL),
                $selection === SubscriptionPlanType::LOCAL->value         => $this->sendSubscriptionPlans($from, SubscriptionPlanType::LOCAL),
                str_starts_with($selection, 'plan_')                     => $this->handleSubscriptionSelection($from, $selection),
                default => null,
            };
            Log::info("return to webhook successfully");
            return response()->json(['status' => 'success'], 200);
        }
    }




    ///////////=======Disable Temporary and use Loacl Welcome Message ======/////////
    /*
      * Handles incoming messages from users on WhatsApp.
      * 1- Triggers an automated response .
      * 2- Sends a WhatsApp message back to the user via a Laravel job.
      */
    private function sendWelcomeMessage($to)
    {
        try {
            $selections = [
                [
                    "title" => "اختر طريقة الدفع",
                    "rows" => [
                        ["id" =>  SubscriptionPlanType::INTERNATIONAL, "title" => "الدفع الدولي (Stripe)", "description" => "استخدم Stripe للمدفوعات الدولية"],
                        ["id" =>  SubscriptionPlanType::LOCAL, "title" => "الدفع المحلي", "description" => "استخدم طرق الدفع الليبي"]
                    ]
                ]
            ];
            $this->whatsapp->sendList($to, "*انظم إلى أكبر منصة للتعليم الإلكتروني في ليبيا*\nمنصة تعليمية توفر شرح وحل أسئلة كتب منهج وزارة التعليم الليبية من الصف الأول الابتدائي للصف الثالث ثانوي في مكان واحد." .
                " \n*للاشتراك،اضغط على الزر أدناه لاختيار الباقة المناسبة لك* \n"  .
                "📞 *للدعم الفني:* تواصل معنا عبر واتساب: +218920118778\n" .
                "🌐 *للمزيد من المعلومات:*(https://addrus.com)", "اختر الباقة", $selections, 'AddrusPay');
        } catch (\Exception $e) {
            Log::error('handleSubscriptionSelection failed', ['error_message' => $e->getMessage(),]);

            $message = "لايمكن اتمام عملية الدفع,  الرجاء المحاولة لاحقاً.";
            $this->whatsapp->sendText($to, $message, 'AddrusPay');
        }
    }

    /////////////Local Welcome message///////////////////
    /*
      * Handles incoming messages from users on WhatsApp.
      * 1- Triggers an automated response .
      * 2- Sends a WhatsApp message back to the user via a Laravel job.
      */
    private function sendLocalWelcomeMessage($to)
    {
        $rows = $this->localSubscriptionPlans();
        if (!$rows) {
            $this->whatsapp->sendText($to, "*انظم إلى أكبر منصة للتعليم الإلكتروني في ليبيا*\nمنصة تعليمية توفر شرح وحل أسئلة كتب منهج وزارة التعليم الليبية من الصف الأول الابتدائي للصف الثالث ثانوي في مكان واحد." .
                "📞 *للدعم الفني:* تواصل معنا عبر واتساب: +218920118778\n" .
                "🌐 *للمزيد من المعلومات:*(https://addrus.com)", 'AddrusPay');
        } else {
            $selections = [
                [
                    "title" =>  " باقات الدفع المحلي",
                    "rows" => $rows
                ]
            ];
            $this->whatsapp->sendList($to, "*انظم إلى أكبر منصة للتعليم الإلكتروني في ليبيا*\nمنصة تعليمية توفر شرح وحل أسئلة كتب منهج وزارة التعليم الليبية من الصف الأول الابتدائي للصف الثالث ثانوي في مكان واحد." .
                " \n*للاشتراك،اضغط على الزر أدناه لاختيار الباقة المناسبة لك* \n"  .
                "📞 *للدعم الفني:* تواصل معنا عبر واتساب: +218920118778\n" .
                "🌐 *للمزيد من المعلومات:*(https://addrus.com)", "اختر الباقة", $selections, 'AddrusPay');
        }
    }


    /////////////////////////// Get Local Subscription Plans ///////////////////////////
    /**
     * This function retrieves local subscription plans from the database.
     * 
     * 1- The retrieved plans are then formatted into a structure for WhatsApp list messages.
     * 2- The final formatted list of subscription plans is returned as an array of data.
     * 
     */
    private function localSubscriptionPlans()
    {
        // Fetch subscription plans from the database
        $plans = SubscriptionPlan::where('type', SubscriptionPlanType::LOCAL)->where('is_active', 1)
            ->get();
        // Format subscription plans into WhatsApp list format
        $rows = [];
        foreach ($plans as $plan) {
            $rows[] = [
                "id" => "plan_" . $plan->id,
                "title" => $plan->title,
                "description" => "فترة الاشتراك " . $plan->period . ' يوم ' . " - " . number_format($plan->price, 2) . " " .  "دينار ليبي"
            ];
        }
        return $rows;
    }


    /////////////// Get Local and International Subscription Plans ////////
    /**
     * Sends a list of available subscription plans to a user via WhatsApp.
     * 
     * - Fetches subscription plans based on the selected payment method.
     *
     */
    private function sendSubscriptionPlans($to, $paymentMethod)
    {
        // Fetch subscription plans from the database
        $plans = SubscriptionPlan::where('type', $paymentMethod)->where('is_active', 1)
            ->get();

        // Call the function to send the message
        return $this->sendWhatsAppSubscriptionList($to, $paymentMethod, $plans);
    }

    /**
     * 
     * - Converts the subscription plans into an array for WhatsApp API.
     * - Differentiates between local and international pricing by adjusting the currency.
     * - Adds a Back option to allow the user to return to the payment selection menu.
     * - Dispatches a job SendWhatsAppPayment to send the formatted message.
     * 
     */
    private function sendWhatsAppSubscriptionList($to, $paymentMethod, $plans)
    {

        // Format subscription plans into WhatsApp list format
        $rows = $this->formatPlanRows($plans, $paymentMethod === SubscriptionPlanType::INTERNATIONAL ? "$" : "دينار ليبي");
        // Add a Back option
        $rows[] = [
            "id" => "back",
            "title" => "اختيار طريقة الدفع",
            "description" => "اضغط هنا للرجوع لاختيار طريقة الدفع"
        ];
        // Prepare the WhatsApp list message;
        $selections = [
            [
                "title" => ($paymentMethod === SubscriptionPlanType::INTERNATIONAL) ? " ياقات الدفع الدولي" : " باقات الدفع المحلي",
                "rows" => $rows
            ]
        ];
        $this->whatsapp->sendList($to, " اختر الباقة المناسبة لك:", "اختر الباقة", $selections, 'AddrusPay');
    }
    private function formatPlanRows($plans, string $currency = 'دينار ليبي'): array
    {
        return $plans->map(function ($plan) use ($currency) {
            return [
                "id" => "plan_" . $plan->id,
                "title" => $plan->title,
                "description" => "فترة الاشتراك {$plan->period} يوم - " . number_format($plan->price, 2) . " {$currency}"
            ];
        })->toArray();
    }


    /**
     * Handles the user subscription plan selection and initiates the payment process.
     *
     * 1- Sends a processing payment message to the user.
     * 2- Retrieves the selected plan from the database.
     * 3- Determines if the payment is international or local.
     * 4- Calls the payment method and generates a payment url.
     * 5- Sends the payment link to the user via WhatsApp.
     * 
     */
    private function handleSubscriptionSelection($to, $planId)
    {
        try {
            Log::info("handleSubscriptionSelection", ['to' => $to, 'planId' => $planId]);

            $this->whatsapp->sendText($to, "جاري معالجة طلب الدفع الخاص بك... \nيرجى الانتظار لبضع ثوانٍ.", 'AddrusPay');

            //Extract the actual plan ID remove plan_ prefix
            $planId = str_replace("plan_", "", $planId);

            // Fetch the plan from the database
            $plan = SubscriptionPlan::where('id', $planId)->first();
            //Determine the payment method based on the plan type
            $paymentResponse = null;
            $validatedData = [
                "phone" => '+' . $to,
                "plan_id" => (int) $planId
            ];
            if ($plan->type == SubscriptionPlanType::INTERNATIONAL) {
                // Initialize Stripe payment process
                $stripeController = new StripePaymentController();

                $paymentResponse = $stripeController->initiatePayment(new  Request($validatedData));
            } else {
                WhatsAppTlync::dispatch('+' . $to, $planId);
            }
        } catch (\Exception $e) {
            Log::error('handleSubscriptionSelection failed', ['error_message' => $e->getMessage(),]);
            $message = "لايمكن اتمام عملية الدفع,  الرجاء المحاولة لاحقاً.";
            $this->whatsapp->sendText($to, $message, 'AddrusPay');
        }
    }



    /**
     * Move it to job

     * Update the Tlync function in the Initiate Paymenat controller to return JSON.
     */
    //=======================TLync=============================//
    // This function searchs for valid subscription
    // public function findSubscription($planId)
    // {

    //     // Getting subscription from subscripions table
    //     $subscription = Subscription::where('subscription_plan_id', $planId)
    //         ->where('status', 0)
    //         ->select('id', 'email', 'password')
    //         ->first();

    //     // Check if subscription exists
    //     if ($subscription) {

    //         // Return the found subscription
    //         return $subscription;
    //     } else {

    //         return null;
    //     }
    // }

    // /**
    //  * Initiates a payment request to T-lync payment gateway.
    //  * 
    //  * 1- Validates the incoming request.
    //  * 2- Extracts the amount, phone number, and subscription plan ID.
    //  * 3- Generates a unique reference for the transaction.
    //  * 4- Validates the uniqueness of that reference.
    //  * 5- Prepares the API payload.
    //  * 6- Calls the T-lync payment gateway API.
    //  * 7- Handles the API response.
    //  * 8- Updates the subscription status and creates a new transaction record.
    //  * 9- Dispatches a job to check the accounts and create more.
    //  * 10- Dispatches a job to check the transaction status.
    //  * 11- Returns a JSON response with the payment URL.
    //  */
    // public function initiateTlyncPayment(Request $request)
    // {
    //     try {
    //         Log::info($request);

    //         // Extract data from the request
    //         $userPhone = $request['phone'];
    //         $chosenPlanId = $request['plan_id'];

    //         // Generate unique reference
    //         $customRef = Str::uuid()->toString();

    //         // Validates the request data
    //         $validator = Validator::make(
    //             [
    //                 'custom_ref' => $customRef,
    //                 'phone' => $userPhone,
    //                 'plan_id' => $chosenPlanId,
    //             ],

    //             [
    //                 'custom_ref' => 'required|string|unique:transactions,uuid',
    //                 'phone' => 'required|regex:/^\+2189\d{8}$/',
    //                 'plan_id' => 'required|integer|exists:subscription_plans,id',
    //             ],
    //         );

    //         // Check if the customer reference did not pass!
    //         if ($validator->fails()) {
    //             throw new ValidationException($validator);
    //         }

    //         // Gets the amount of the chosen plan
    //         $amount = SubscriptionPlan::where('id', $chosenPlanId)->value('price');

    //         // Prepare API payload
    //         $data = [
    //             'id' => config('api.store_id'),
    //             'amount' => $amount,
    //             'phone' => $userPhone,
    //             'backend_url' => config('api.backend_url'),
    //             'custom_ref' => $customRef,
    //             'frontend_url' => config('api.whatsapp_frontend_url') . '/' . $customRef,
    //             'failed_front_end_url' => config('api.whatsapp_failed_frontend_url') . '/' . $customRef,
    //         ];

    //         Log::info('Payment Payload', [$data]);

    //         // Call payment gateway API
    //         try {

    //             $response = Http::timeout(60)
    //                 ->withHeaders(['Accept' => 'application/json'])
    //                 ->withToken(config('api.api_token'))
    //                 ->post(config('api.payment_api_live'), $data);
    //         } catch (\Exception $error) {

    //             Log::error('Tlync Call Failed: ' . $error->getMessage());

    //             return response()->json([
    //                 'status' => false,
    //                 'message' => 'تعذر الاتصال بمزود الدفع'
    //             ], 500);
    //         }

    //         // Handle API response
    //         if ($response->successful()) {
    //             $responseData = $response->json();

    //             try {
    //                 DB::transaction(function () use ($chosenPlanId, $data) {

    //                     // Get a subscription
    //                     $subscription = $this->findSubscription($chosenPlanId);

    //                     // Checking and creating accounts
    //                     CheckUsersAndCreate::dispatch();

    //                     if (!$subscription) {
    //                         throw new \Exception('Somthing wrong with finding subscription');
    //                     }
    //                     // Change the status to taken
    //                     $subscription->status = 1;
    //                     $subscription->save();

    //                     $transaction = Transaction::create([
    //                         'email' => '',
    //                         'phone' => $data['phone'],
    //                         'subscription_id' => $subscription->id,
    //                         'payment_provider' => PaymentProvider::TLYNC,
    //                         'amount' => $data['amount'],
    //                         'currency' => Currency::LYD,
    //                         'status' => TransactionStatus::PENDING,
    //                         'uuid' => $data['custom_ref'],
    //                     ]);
    //                     $transaction->load('subscription');

    //                     CheckTransactionStatus::dispatch($transaction)
    //                         ->delay(now()->addMinutes(1));
    //                 });

    //                 return response()->json([
    //                     'status' => true,
    //                     'url' => $responseData['url'],
    //                 ]);
    //             } catch (\Exception $error) {
    //                 Log::error('Transaction Failed: ' . $error->getMessage());
    //                 return response()->json([
    //                     'status' => false,
    //                     'message' => 'حدث خطأ أثناء معالجة الطلب يرجى المحاولة مرة أخرى'
    //                 ], 500);
    //             }
    //         }

    //         // Handle failed API response
    //         Log::error('Tlync API Error: ' . $response->body());
    //         return response()->json([
    //             'status' => false,
    //             'message' => ' حدث خطأ أثناء معالجة عملية الدفع'
    //         ], 400);
    //     } catch (\Exception $error) {
    //         Log::error('System Error: ' . $error->getMessage());
    //         return response()->json([
    //             'status' => false,
    //             'message' => 'حدث خطأ أثناء معالجة الطلب يرجى المحاولة مرة أخرى'
    //         ], 500);
    //     }
    // }

    // This function show success page with the subscription after successful transaction
    public function showPaymentSucceeded($customRef)
    {

        // Null check for the custom reference
        if (!$customRef) {
            return view('local.error', [
                'error' => 'تم إلغاء العملية',
                'message' => 'حدث خطأ أثناء عملية الدفع, يرجى التأكد من البيانات والمحاولة مرة أخرى.',
            ]);
        }

        $transaction = Transaction::with('subscription')
            ->where('uuid', $customRef)
            ->first();

        return view('local.reciept', [
            'email' => $transaction->subscription->email,
            'password' => $transaction->subscription->password,
        ]);
    }

    // This function setting the status in case of a failed payment
    public function showPaymentFailed($customRef)
    {

        // Null check for the custom reference
        if (!$customRef) {
            return view('local.error', [
                'error' => 'إنتهت عملية الدفع',
                'message' => 'يمكن الخروج أو العودة إلى الصفحة الرئيسية.',
            ]);
        }

        $transaction = Transaction::with('subscription')
            ->where('uuid', $customRef)
            ->first();

        // Check if the transaction is already completed
        if ($transaction->status === TransactionStatus::COMPLETED) {
            return view('local.error', [
                'error' => 'إنتهت عملية الدفع',
                'message' => 'يمكن الخروج أو العودة إلى الصفحة الرئيسية.',
            ]);
        }

        // Setting the transaction to failed
        $transaction->status = TransactionStatus::FAILED;
        $transaction->save();

        // Setting subscription status back to not taken
        $transaction->subscription->status = 0;
        $transaction->subscription->save();

        return view('local.error', [
            'error' => 'تم إلغاء العملية',
            'message' => 'حدث خطأ أثناء عملية الدفع, يرجى التأكد من البيانات والمحاولة مرة أخرى.',
        ]);
    }
}
