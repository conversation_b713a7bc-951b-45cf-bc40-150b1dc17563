const interfaceTranslations = {
  selectedCountryAriaLabel: "Odabrana zemlja",
  noCountrySelected: "Zemlja nije odabrana",
  countryListAriaLabel: "Lista zemalja",
  searchPlaceholder: "<PERSON><PERSON><PERSON><PERSON>",
  zeroSearchResults: "Nema pronađenih rezultata",
  oneSearchResult: "Pronađen 1 rezultat",
  multipleSearchResults: "${count} rezultata pronađeno",
  // additional countries (not supported by country-list library)
  ac: "Ascension",
  xk: "Kosovo"
};
export default interfaceTranslations;
