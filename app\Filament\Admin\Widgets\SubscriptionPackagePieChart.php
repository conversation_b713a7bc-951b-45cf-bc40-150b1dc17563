<?php


namespace App\Filament\Admin\Widgets;

use App\Models\AskMe\SubscriptionPackage;
use Filament\Widgets\ChartWidget;

/**
 * Subscription Package Pie Chart Widget
 *
 * Displays a pie chart showing the distribution of subscriptions across different packages.
 */
class SubscriptionPackagePieChart extends ChartWidget
{

    protected static ?string $heading = 'نسبة مبيعات كل باقة اشتراك';
    protected static ?int $sort = 4;
    protected static ?string $maxHeight = '300px';

    /**
     * Generate the data for the subscription package pie chart
     *
     * This method retrieves all subscription packages with their subscription counts
     * and formats the data for display in a pie chart.
     *
     * @return array The chart data configuration
     */
    protected function getData(): array
    {
        // Retrieve all subscription packages with their subscription counts
        $packages = SubscriptionPackage::withCount('subscriptions')->get();

        return [
            'datasets' => [
                [
                    // Dataset configuration
                    'label' => __('filament-panels.dashboard.packages_sold'),
                    // Number of subscriptions for each package
                    'data' => $packages->pluck('subscriptions_count'),
                    // Color palette for the pie chart segments
                    'backgroundColor' => [
                        '#3b82f6', // Blue
                        '#f97316', // Orange
                        '#10b981', // Green
                        '#f43f5e', // Pink
                        '#a855f7', // Purple
                    ],
                ],
            ],
            // Package names as labels for the chart segments
            'labels' => $packages->pluck('name'),
        ];
    }

    protected function getType(): string
    {
        return 'pie';
    }
}
