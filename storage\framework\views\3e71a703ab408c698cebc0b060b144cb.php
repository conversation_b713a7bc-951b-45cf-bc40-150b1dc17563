<head>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: 'DejaVu Sans', sans-serif;
            direction: rtl;
            text-align: right;
            font-size: 14px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ccc;
            padding: 8px;
            text-align: center;
        }
        th {
            background-color: #eee;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .divider {
            border: none;
            border-top: 2px solid #333;
            margin: 20px 0;
        }
    </style>
</head>

<body>

    <div class="header">
        <h1>أدرس للتعليم الإلكتروني</h1>
        <h3>خدمة اسألني</h3>

        <p style="font-size: 18px; font-weight: bold; margin-top: 10px;">
            تقرير الإيرادات
        </p>

        
        <p style="margin-top: 5px;">
            <?php if($range == 'month' && $month): ?>
                لشهر <?php echo e($month); ?>

            <?php elseif($range == 'year'): ?>
                للسنة الحالية: <?php echo e(now()->format('Y')); ?>

            <?php elseif($range == 'week'): ?>
                للأسبوع من <?php echo e($weekStart); ?> إلى <?php echo e($weekEnd); ?>

            <?php else: ?>
                للفترة المحددة
            <?php endif; ?>
        </p>

        <p style="margin-top: 10px; font-size: 14px; color: #666;">
            هذا التقرير يعرض ملخص الإيرادات اليومية وعدد العمليات المنفذة خلال الفترة الزمنية المختارة.
        </p>
    </div>

    <hr class="divider">

    <div>
        <p><strong>عدد العمليات:</strong> <?php echo e($transactionsCount); ?></p>
        <p><strong>الإيرادات الكلية:</strong> <?php echo e(number_format($totalRevenue, 2)); ?> دينار</p>
    </div>

    <hr class="divider">

    <div>
        <h3 style="margin-top: 20px;">تفاصيل الإيرادات اليومية:</h3>
        <table>
            <thead>
                <tr>
                    <th>التاريخ</th>
                    <th>عدد العمليات</th>
                    <th>الإيرادات</th>
                </tr>
            </thead>
            <tbody>
                <?php $__empty_1 = true; $__currentLoopData = $dailyRevenues; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $day): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <tr>
                        <td><?php echo e(\Carbon\Carbon::parse($day->date)->format('d/m/Y')); ?></td>
                        <td><?php echo e($day->transactions_count); ?></td>
                        <td><?php echo e(number_format($day->total_amount, 2)); ?> دينار</td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <tr>
                        <td colspan="3" style="text-align: center; padding: 10px;">لا توجد بيانات متاحة.</td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

</body>
<?php /**PATH C:\xampp\htdocs\work\addrus-pay\resources\views/exports/revenue.blade.php ENDPATH**/ ?>