<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Enums\PaymentProvider;
use App\Enums\Currency;
use App\Enums\TransactionStatus;

class Transaction extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'email',
        'phone',
        'subscription_id',
        'payment_provider',
        'amount',
        'currency',
        'status',
        'uuid',
        'external_ref',
        'email_send',
        'text_send'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'subscription_id' => 'integer',
        'amount' => 'decimal:2',
        'email_send' => 'boolean',
        'text_send' => 'boolean',
        'payment_provider' => PaymentProvider::class,
        'currency' => Currency::class,
        'status' => TransactionStatus::class,
    ];

    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class);
    }

    public function paymentCallbacks(): HasMany
    {
        return $this->hasMany(PaymentCallback::class);
    }
}
